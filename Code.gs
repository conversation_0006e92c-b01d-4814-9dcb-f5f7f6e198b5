// ===== KONFIGURASI APLIKASI =====
const CONFIG = {
  SPREADSHEET_NAME: 'pos',
  SHEETS: {
    USERS: 'users',
    BARANG: 'barang',
    KATEGORI: 'kategori',
    SATUAN: 'satuan',
    PENJUALAN: 'penjualan',
    PENJUALAN_ITEM: 'penjualan_item',
    PELANGGAN: 'pelanggan'
  }
};

// ===== FUNGSI UTAMA =====
function doGet(e) {
  const page = e.parameter.page || 'index';

  // Check if system needs initialization (except for setup page)
  if (page !== 'setup') {
    try {
      const userSheet = getSheet(CONFIG.SHEETS.USERS);
      const userData = userSheet.getDataRange().getValues();
      if (userData.length <= 1) { // Only header or empty
        // Redirect to setup page
        return HtmlService.createTemplateFromFile('setup').evaluate()
          .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
      }
    } catch (error) {
      console.log('Initialization check error:', error);
      // If there's an error accessing sheets, show setup page
      return HtmlService.createTemplateFromFile('setup').evaluate()
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
    }
  }

  const user = getSessionUser();

  if (!user && page !== 'login' && page !== 'setup') {
    return HtmlService.createTemplateFromFile('login').evaluate()
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
  }

  let template;
  switch(page) {
    case 'login':
      template = HtmlService.createTemplateFromFile('login');
      break;
    case 'setup':
      template = HtmlService.createTemplateFromFile('setup');
      break;
    case 'kasir':
      template = HtmlService.createTemplateFromFile('kasir');
      break;
    case 'barang':
      template = HtmlService.createTemplateFromFile('barang');
      break;
    case 'kategori':
      template = HtmlService.createTemplateFromFile('kategori');
      break;
    case 'satuan':
      template = HtmlService.createTemplateFromFile('satuan');
      break;
    case 'pelanggan':
      template = HtmlService.createTemplateFromFile('pelanggan');
      break;
    case 'laporan':
      template = HtmlService.createTemplateFromFile('laporan');
      break;
    case 'users':
      template = HtmlService.createTemplateFromFile('users');
      break;
    case 'debug':
      template = HtmlService.createTemplateFromFile('debug');
      break;
    case 'test_layout':
      template = HtmlService.createTemplateFromFile('test_layout');
      break;
    default:
      template = HtmlService.createTemplateFromFile('index');
  }

  template.user = user;
  return template.evaluate()
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

function doPost(e) {
  try {
    const action = e.parameter.action;

    switch(action) {
      case 'login':
        return ContentService.createTextOutput(
          JSON.stringify(login(e.parameter.username, e.parameter.password))
        ).setMimeType(ContentService.MimeType.JSON);
      case 'logout':
        return ContentService.createTextOutput(
          JSON.stringify(logout())
        ).setMimeType(ContentService.MimeType.JSON);
      default:
        return ContentService.createTextOutput(
          JSON.stringify({ success: false, message: 'Action tidak dikenali' })
        ).setMimeType(ContentService.MimeType.JSON);
    }
  } catch (e) {
    return ContentService.createTextOutput(
      JSON.stringify({ success: false, message: 'Error: ' + e.toString() })
    ).setMimeType(ContentService.MimeType.JSON);
  }
}

// ===== FUNGSI INCLUDE =====
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

// ===== DATABASE HELPER =====
function getSpreadsheet() {
  try {
    return SpreadsheetApp.openByUrl(
      DriveApp.getFilesByName(CONFIG.SPREADSHEET_NAME).next().getUrl()
    );
  } catch (e) {
    throw new Error('Spreadsheet "' + CONFIG.SPREADSHEET_NAME + '" tidak ditemukan');
  }
}

function getSheet(sheetName) {
  const ss = getSpreadsheet();
  let sheet = ss.getSheetByName(sheetName);

  if (!sheet) {
    sheet = ss.insertSheet(sheetName);
    initializeSheet(sheetName, sheet);
  }

  return sheet;
}

function initializeSheet(sheetName, sheet) {
  let headers = [];

  switch(sheetName) {
    case CONFIG.SHEETS.USERS:
      headers = ['id', 'username', 'password', 'nama_lengkap', 'role', 'status', 'created_at', 'last_login'];
      break;
    case CONFIG.SHEETS.KATEGORI:
      headers = ['id', 'nama_kategori', 'deskripsi', 'status', 'created_at'];
      break;
    case CONFIG.SHEETS.SATUAN:
      headers = ['id', 'nama_satuan', 'singkatan', 'status', 'created_at'];
      break;
    case CONFIG.SHEETS.BARANG:
      headers = ['id', 'kode_barang', 'nama_barang', 'kategori_id', 'satuan_id', 'harga_beli', 'harga_jual', 'stok_minimum', 'stok_saat_ini', 'gambar_url', 'status', 'created_at', 'updated_at'];
      break;
    case CONFIG.SHEETS.PELANGGAN:
      headers = ['id', 'kode_pelanggan', 'nama_pelanggan', 'telepon', 'email', 'alamat', 'tipe', 'diskon_persen', 'total_pembelian', 'poin_reward', 'status', 'created_at'];
      break;
    case CONFIG.SHEETS.PENJUALAN:
      headers = ['id', 'nomor_transaksi', 'tanggal_transaksi', 'kasir_id', 'pelanggan_id', 'subtotal', 'diskon_persen', 'diskon_nominal', 'pajak_persen', 'pajak_nominal', 'total_bayar', 'metode_pembayaran', 'cash_amount', 'card_amount', 'digital_amount', 'kembalian', 'status', 'catatan', 'created_at'];
      break;
    case CONFIG.SHEETS.PENJUALAN_ITEM:
      headers = ['id', 'penjualan_id', 'barang_id', 'kode_barang', 'nama_barang', 'harga_satuan', 'quantity', 'diskon_item_persen', 'diskon_item_nominal', 'subtotal_item', 'created_at'];
      break;
  }

  if (headers.length > 0) {
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
  }
}

// ===== AUTHENTICATION =====
function login(username, password) {
  try {
    console.log('Login attempt for username:', username);

    const sheet = getSheet(CONFIG.SHEETS.USERS);
    const data = sheet.getDataRange().getValues();

    console.log('User data rows:', data.length);

    for (let i = 1; i < data.length; i++) {
      console.log('Checking user:', data[i][1], 'status:', data[i][5]);

      if (data[i][1] === username && data[i][2] === password && data[i][5] === 'aktif') {
        const user = {
          id: data[i][0],
          username: data[i][1],
          nama_lengkap: data[i][3],
          role: data[i][4]
        };

        console.log('Login successful for user:', user);

        // Update last login
        sheet.getRange(i + 1, 8).setValue(new Date());

        // Set session
        PropertiesService.getScriptProperties().setProperty('user_session', JSON.stringify(user));

        return { success: true, user: user };
      }
    }

    console.log('Login failed - no matching user found');
    return { success: false, message: 'Username atau password salah' };
  } catch (e) {
    console.error('Login error:', e);
    return { success: false, message: 'Error: ' + e.toString() };
  }
}

function logout() {
  PropertiesService.getScriptProperties().deleteProperty('user_session');
  return { success: true };
}

function getSessionUser() {
  try {
    const userSession = PropertiesService.getScriptProperties().getProperty('user_session');
    return userSession ? JSON.parse(userSession) : null;
  } catch (e) {
    return null;
  }
}

// ===== DEBUG FUNCTIONS =====
function debugGetUsers() {
  try {
    const sheet = getSheet(CONFIG.SHEETS.USERS);
    const data = sheet.getDataRange().getValues();

    return {
      success: true,
      totalRows: data.length,
      headers: data[0],
      data: data.slice(1)
    };
  } catch (e) {
    return {
      success: false,
      error: e.toString()
    };
  }
}

// ===== CRUD OPERATIONS =====
function getNextId(sheetName) {
  const sheet = getSheet(sheetName);
  const data = sheet.getDataRange().getValues();
  let maxId = 0;

  for (let i = 1; i < data.length; i++) {
    if (data[i][0] > maxId) {
      maxId = data[i][0];
    }
  }

  return maxId + 1;
}

// ===== KATEGORI FUNCTIONS =====
function getKategoriList() {
  try {
    const sheet = getSheet(CONFIG.SHEETS.KATEGORI);
    const data = sheet.getDataRange().getValues();
    const result = [];

    for (let i = 1; i < data.length; i++) {
      if (data[i][0]) {
        result.push({
          id: data[i][0],
          nama_kategori: data[i][1],
          deskripsi: data[i][2],
          status: data[i][3],
          created_at: data[i][4]
        });
      }
    }

    return { success: true, data: result };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function saveKategori(data) {
  try {
    const sheet = getSheet(CONFIG.SHEETS.KATEGORI);

    if (data.id) {
      // Update existing
      const range = sheet.getDataRange();
      const values = range.getValues();

      for (let i = 1; i < values.length; i++) {
        if (values[i][0] == data.id) {
          sheet.getRange(i + 1, 2, 1, 3).setValues([[
            data.nama_kategori,
            data.deskripsi,
            data.status
          ]]);
          return { success: true, message: 'Kategori berhasil diupdate' };
        }
      }
    } else {
      // Insert new
      const newId = getNextId(CONFIG.SHEETS.KATEGORI);
      sheet.appendRow([
        newId,
        data.nama_kategori,
        data.deskripsi,
        data.status || 'aktif',
        new Date()
      ]);
      return { success: true, message: 'Kategori berhasil ditambahkan' };
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function deleteKategori(id) {
  try {
    const sheet = getSheet(CONFIG.SHEETS.KATEGORI);
    const range = sheet.getDataRange();
    const values = range.getValues();

    for (let i = 1; i < values.length; i++) {
      if (values[i][0] == id) {
        sheet.deleteRow(i + 1);
        return { success: true, message: 'Kategori berhasil dihapus' };
      }
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

// ===== SATUAN FUNCTIONS =====
function getSatuanList() {
  try {
    const sheet = getSheet(CONFIG.SHEETS.SATUAN);
    const data = sheet.getDataRange().getValues();
    const result = [];

    for (let i = 1; i < data.length; i++) {
      if (data[i][0]) {
        result.push({
          id: data[i][0],
          nama_satuan: data[i][1],
          singkatan: data[i][2],
          status: data[i][3],
          created_at: data[i][4]
        });
      }
    }

    return { success: true, data: result };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function saveSatuan(data) {
  try {
    const sheet = getSheet(CONFIG.SHEETS.SATUAN);

    if (data.id) {
      // Update existing
      const range = sheet.getDataRange();
      const values = range.getValues();

      for (let i = 1; i < values.length; i++) {
        if (values[i][0] == data.id) {
          sheet.getRange(i + 1, 2, 1, 3).setValues([[
            data.nama_satuan,
            data.singkatan,
            data.status
          ]]);
          return { success: true, message: 'Satuan berhasil diupdate' };
        }
      }
    } else {
      // Insert new
      const newId = getNextId(CONFIG.SHEETS.SATUAN);
      sheet.appendRow([
        newId,
        data.nama_satuan,
        data.singkatan,
        data.status || 'aktif',
        new Date()
      ]);
      return { success: true, message: 'Satuan berhasil ditambahkan' };
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function deleteSatuan(id) {
  try {
    const sheet = getSheet(CONFIG.SHEETS.SATUAN);
    const range = sheet.getDataRange();
    const values = range.getValues();

    for (let i = 1; i < values.length; i++) {
      if (values[i][0] == id) {
        sheet.deleteRow(i + 1);
        return { success: true, message: 'Satuan berhasil dihapus' };
      }
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

// ===== BARANG FUNCTIONS =====
function getBarangList() {
  try {
    const sheet = getSheet(CONFIG.SHEETS.BARANG);
    const data = sheet.getDataRange().getValues();
    const result = [];

    // Get kategori and satuan for join
    const kategoriData = getKategoriList().data || [];
    const satuanData = getSatuanList().data || [];

    for (let i = 1; i < data.length; i++) {
      if (data[i][0]) {
        const kategori = kategoriData.find(k => k.id == data[i][3]);
        const satuan = satuanData.find(s => s.id == data[i][4]);

        result.push({
          id: data[i][0],
          kode_barang: data[i][1],
          nama_barang: data[i][2],
          kategori_id: data[i][3],
          kategori_nama: kategori ? kategori.nama_kategori : '',
          satuan_id: data[i][4],
          satuan_nama: satuan ? satuan.nama_satuan : '',
          harga_beli: data[i][5],
          harga_jual: data[i][6],
          stok_minimum: data[i][7],
          stok_saat_ini: data[i][8],
          gambar_url: data[i][9],
          status: data[i][10],
          created_at: data[i][11],
          updated_at: data[i][12]
        });
      }
    }

    return { success: true, data: result };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function saveBarang(data) {
  try {
    const sheet = getSheet(CONFIG.SHEETS.BARANG);

    if (data.id) {
      // Update existing
      const range = sheet.getDataRange();
      const values = range.getValues();

      for (let i = 1; i < values.length; i++) {
        if (values[i][0] == data.id) {
          sheet.getRange(i + 1, 2, 1, 12).setValues([[
            data.kode_barang,
            data.nama_barang,
            data.kategori_id,
            data.satuan_id,
            data.harga_beli,
            data.harga_jual,
            data.stok_minimum,
            data.stok_saat_ini,
            data.gambar_url || '',
            data.status,
            values[i][11], // keep created_at
            new Date() // updated_at
          ]]);
          return { success: true, message: 'Barang berhasil diupdate' };
        }
      }
    } else {
      // Insert new
      const newId = getNextId(CONFIG.SHEETS.BARANG);
      sheet.appendRow([
        newId,
        data.kode_barang,
        data.nama_barang,
        data.kategori_id,
        data.satuan_id,
        data.harga_beli,
        data.harga_jual,
        data.stok_minimum,
        data.stok_saat_ini,
        data.gambar_url || '',
        data.status || 'aktif',
        new Date(),
        new Date()
      ]);
      return { success: true, message: 'Barang berhasil ditambahkan' };
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function deleteBarang(id) {
  try {
    const sheet = getSheet(CONFIG.SHEETS.BARANG);
    const range = sheet.getDataRange();
    const values = range.getValues();

    for (let i = 1; i < values.length; i++) {
      if (values[i][0] == id) {
        sheet.deleteRow(i + 1);
        return { success: true, message: 'Barang berhasil dihapus' };
      }
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function searchBarang(keyword) {
  try {
    const result = getBarangList();
    if (!result.success) return result;

    const filtered = result.data.filter(item =>
      item.kode_barang.toLowerCase().includes(keyword.toLowerCase()) ||
      item.nama_barang.toLowerCase().includes(keyword.toLowerCase())
    );

    return { success: true, data: filtered };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

// ===== IMPORT/EXPORT BARANG FUNCTIONS =====
function exportBarangToXLSX() {
  try {
    const result = getBarangList();
    if (!result.success) return result;

    // Create new spreadsheet for export
    const ss = SpreadsheetApp.create('Export_Barang_' + Utilities.formatDate(new Date(), 'GMT+7', 'yyyyMMdd_HHmmss'));
    const sheet = ss.getActiveSheet();

    // Headers
    const headers = [
      'ID', 'Kode Barang', 'Nama Barang', 'Kategori', 'Satuan',
      'Harga Beli', 'Harga Jual', 'Stok Minimum', 'Stok Saat Ini',
      'Status', 'Tanggal Dibuat'
    ];

    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');

    // Data
    const data = result.data.map(item => [
      item.id,
      item.kode_barang,
      item.nama_barang,
      item.kategori_nama,
      item.satuan_nama,
      item.harga_beli,
      item.harga_jual,
      item.stok_minimum,
      item.stok_saat_ini,
      item.status,
      item.created_at
    ]);

    if (data.length > 0) {
      sheet.getRange(2, 1, data.length, headers.length).setValues(data);
    }

    // Auto resize columns
    sheet.autoResizeColumns(1, headers.length);

    const url = ss.getUrl();
    DriveApp.getFileById(ss.getId()).setSharing(DriveApp.Access.ANYONE_WITH_LINK, DriveApp.Permission.VIEW);

    return {
      success: true,
      message: 'Export berhasil',
      url: url,
      filename: 'Export_Barang_' + Utilities.formatDate(new Date(), 'GMT+7', 'yyyyMMdd_HHmmss')
    };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function getImportTemplate() {
  try {
    // Create template spreadsheet
    const ss = SpreadsheetApp.create('Template_Import_Barang');
    const sheet = ss.getActiveSheet();

    // Headers
    const headers = [
      'kode_barang', 'nama_barang', 'kategori_id', 'satuan_id',
      'harga_beli', 'harga_jual', 'stok_minimum', 'stok_saat_ini', 'status'
    ];

    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');

    // Sample data
    const sampleData = [
      ['BRG001', 'Contoh Barang 1', 1, 1, 5000, 7000, 10, 100, 'aktif'],
      ['BRG002', 'Contoh Barang 2', 2, 2, 3000, 4500, 5, 50, 'aktif']
    ];

    sheet.getRange(2, 1, sampleData.length, headers.length).setValues(sampleData);
    sheet.autoResizeColumns(1, headers.length);

    const url = ss.getUrl();
    DriveApp.getFileById(ss.getId()).setSharing(DriveApp.Access.ANYONE_WITH_LINK, DriveApp.Permission.VIEW);

    return {
      success: true,
      message: 'Template berhasil dibuat',
      url: url
    };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function importBarangFromXLSX(fileId) {
  try {
    const file = DriveApp.getFileById(fileId);
    const blob = file.getBlob();
    const ss = SpreadsheetApp.openByUrl(DriveApp.createFile(blob).getUrl());
    const sheet = ss.getActiveSheet();
    const data = sheet.getDataRange().getValues();

    if (data.length < 2) {
      return { success: false, message: 'File tidak memiliki data untuk diimport' };
    }

    const headers = data[0];
    const requiredHeaders = ['kode_barang', 'nama_barang', 'kategori_id', 'satuan_id', 'harga_beli', 'harga_jual', 'stok_minimum', 'stok_saat_ini'];

    // Validate headers
    for (let header of requiredHeaders) {
      if (!headers.includes(header)) {
        return { success: false, message: `Header '${header}' tidak ditemukan` };
      }
    }

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    for (let i = 1; i < data.length; i++) {
      try {
        const row = data[i];
        const barangData = {
          kode_barang: row[headers.indexOf('kode_barang')],
          nama_barang: row[headers.indexOf('nama_barang')],
          kategori_id: row[headers.indexOf('kategori_id')],
          satuan_id: row[headers.indexOf('satuan_id')],
          harga_beli: row[headers.indexOf('harga_beli')],
          harga_jual: row[headers.indexOf('harga_jual')],
          stok_minimum: row[headers.indexOf('stok_minimum')],
          stok_saat_ini: row[headers.indexOf('stok_saat_ini')],
          status: row[headers.indexOf('status')] || 'aktif'
        };

        const result = saveBarang(barangData);
        if (result.success) {
          successCount++;
        } else {
          errorCount++;
          errors.push(`Baris ${i + 1}: ${result.message}`);
        }
      } catch (e) {
        errorCount++;
        errors.push(`Baris ${i + 1}: ${e.toString()}`);
      }
    }

    // Clean up temporary file
    DriveApp.getFileById(ss.getId()).setTrashed(true);

    return {
      success: true,
      message: `Import selesai. Berhasil: ${successCount}, Error: ${errorCount}`,
      successCount: successCount,
      errorCount: errorCount,
      errors: errors
    };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function importBarangFromJSON(jsonData) {
  try {
    if (!jsonData || jsonData.length === 0) {
      return { success: false, message: 'Data tidak ditemukan' };
    }

    const requiredFields = ['kode_barang', 'nama_barang', 'kategori_id', 'satuan_id', 'harga_beli', 'harga_jual'];

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    for (let i = 0; i < jsonData.length; i++) {
      try {
        const row = jsonData[i];

        // Validate required fields
        let missingFields = [];
        for (let field of requiredFields) {
          if (!row[field]) {
            missingFields.push(field);
          }
        }

        if (missingFields.length > 0) {
          errorCount++;
          errors.push(`Baris ${i + 1}: Field wajib kosong: ${missingFields.join(', ')}`);
          continue;
        }

        const barangData = {
          kode_barang: row.kode_barang,
          nama_barang: row.nama_barang,
          kategori_id: row.kategori_id,
          satuan_id: row.satuan_id,
          harga_beli: row.harga_beli,
          harga_jual: row.harga_jual,
          stok_minimum: row.stok_minimum || 0,
          stok_saat_ini: row.stok_saat_ini || 0,
          status: row.status || 'aktif'
        };

        const result = saveBarang(barangData);
        if (result.success) {
          successCount++;
        } else {
          errorCount++;
          errors.push(`Baris ${i + 1}: ${result.message}`);
        }
      } catch (e) {
        errorCount++;
        errors.push(`Baris ${i + 1}: ${e.toString()}`);
      }
    }

    return {
      success: true,
      message: `Import selesai. Berhasil: ${successCount}, Error: ${errorCount}`,
      successCount: successCount,
      errorCount: errorCount,
      errors: errors
    };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

// ===== PELANGGAN FUNCTIONS =====
function getPelangganList() {
  try {
    const sheet = getSheet(CONFIG.SHEETS.PELANGGAN);
    const data = sheet.getDataRange().getValues();
    const result = [];

    for (let i = 1; i < data.length; i++) {
      if (data[i][0]) {
        result.push({
          id: data[i][0],
          kode_pelanggan: data[i][1],
          nama_pelanggan: data[i][2],
          telepon: data[i][3],
          email: data[i][4],
          alamat: data[i][5],
          tipe: data[i][6],
          diskon_persen: data[i][7],
          total_pembelian: data[i][8],
          poin_reward: data[i][9],
          status: data[i][10],
          created_at: data[i][11]
        });
      }
    }

    return { success: true, data: result };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

// ===== DASHBOARD FUNCTIONS =====
function getDashboardStats() {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // Get today's sales
    const penjualanSheet = getSheet(CONFIG.SHEETS.PENJUALAN);
    const penjualanData = penjualanSheet.getDataRange().getValues();

    let totalSalesToday = 0;
    let totalRevenueToday = 0;

    for (let i = 1; i < penjualanData.length; i++) {
      const transactionDate = new Date(penjualanData[i][2]);
      if (transactionDate >= startOfDay && penjualanData[i][16] === 'selesai') {
        totalSalesToday++;
        totalRevenueToday += penjualanData[i][10] || 0;
      }
    }

    // Get total products
    const barangSheet = getSheet(CONFIG.SHEETS.BARANG);
    const barangData = barangSheet.getDataRange().getValues();
    const totalProducts = barangData.length - 1;

    // Get low stock count
    let lowStockCount = 0;
    for (let i = 1; i < barangData.length; i++) {
      if (barangData[i][8] <= barangData[i][7]) { // stok_saat_ini <= stok_minimum
        lowStockCount++;
      }
    }

    return {
      success: true,
      totalSalesToday: totalSalesToday,
      totalRevenueToday: totalRevenueToday,
      totalProducts: totalProducts,
      lowStockCount: lowStockCount
    };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function getRecentTransactions() {
  try {
    const sheet = getSheet(CONFIG.SHEETS.PENJUALAN);
    const data = sheet.getDataRange().getValues();
    const result = [];

    // Get last 10 transactions
    for (let i = Math.max(1, data.length - 10); i < data.length; i++) {
      if (data[i][0]) {
        result.push({
          id: data[i][0],
          nomor_transaksi: data[i][1],
          tanggal_transaksi: data[i][2],
          total_bayar: data[i][10],
          status: data[i][16],
          created_at: data[i][18]
        });
      }
    }

    return result.reverse(); // Most recent first
  } catch (e) {
    return [];
  }
}

function getLowStockItems() {
  try {
    const sheet = getSheet(CONFIG.SHEETS.BARANG);
    const data = sheet.getDataRange().getValues();
    const result = [];

    for (let i = 1; i < data.length; i++) {
      if (data[i][0] && data[i][8] <= data[i][7]) { // stok_saat_ini <= stok_minimum
        result.push({
          id: data[i][0],
          kode_barang: data[i][1],
          nama_barang: data[i][2],
          stok_saat_ini: data[i][8],
          stok_minimum: data[i][7]
        });
      }
    }

    return result;
  } catch (e) {
    return [];
  }
}

function getTopProducts() {
  try {
    // This would require aggregating data from penjualan_item
    // For now, return empty array
    return [];
  } catch (e) {
    return [];
  }
}

function getSalesChartData() {
  try {
    const today = new Date();
    const labels = [];
    const values = [];

    // Get last 7 days
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      labels.push(Utilities.formatDate(date, 'GMT+7', 'dd/MM'));

      // Count sales for this date
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

      const penjualanSheet = getSheet(CONFIG.SHEETS.PENJUALAN);
      const penjualanData = penjualanSheet.getDataRange().getValues();

      let dailySales = 0;
      for (let j = 1; j < penjualanData.length; j++) {
        const transactionDate = new Date(penjualanData[j][2]);
        if (transactionDate >= startOfDay && transactionDate < endOfDay && penjualanData[j][16] === 'selesai') {
          dailySales += penjualanData[j][10] || 0;
        }
      }

      values.push(dailySales);
    }

    return { labels: labels, values: values };
  } catch (e) {
    return { labels: [], values: [] };
  }
}

// ===== TRANSACTION FUNCTIONS =====
function processTransaction(transactionData) {
  try {
    const user = getSessionUser();
    if (!user) {
      return { success: false, message: 'User tidak terautentikasi' };
    }

    // Validate items
    if (!transactionData.items || transactionData.items.length === 0) {
      return { success: false, message: 'Tidak ada item dalam transaksi' };
    }

    // Calculate totals
    let subtotal = 0;
    for (let item of transactionData.items) {
      subtotal += item.harga_jual * item.quantity;
    }

    const discountPercent = transactionData.discount_percent || 0;
    const discountNominal = subtotal * (discountPercent / 100);
    const afterDiscount = subtotal - discountNominal;
    const taxPercent = 10;
    const taxNominal = afterDiscount * (taxPercent / 100);
    const totalBayar = afterDiscount + taxNominal;

    // Validate payment
    if (transactionData.total_paid < totalBayar) {
      return { success: false, message: 'Jumlah pembayaran kurang' };
    }

    const kembalian = transactionData.total_paid - totalBayar;

    // Generate transaction number
    const nomorTransaksi = generateTransactionNumber();

    // Save to penjualan sheet
    const penjualanSheet = getSheet(CONFIG.SHEETS.PENJUALAN);
    const penjualanId = getNextId(CONFIG.SHEETS.PENJUALAN);

    const paymentMethod = transactionData.payment_methods.join(',');

    penjualanSheet.appendRow([
      penjualanId,
      nomorTransaksi,
      new Date(),
      user.id,
      transactionData.customer_id || null,
      subtotal,
      discountPercent,
      discountNominal,
      taxPercent,
      taxNominal,
      totalBayar,
      paymentMethod,
      transactionData.payment_amounts.cash_amount || 0,
      transactionData.payment_amounts.card_amount || 0,
      transactionData.payment_amounts.digital_amount || 0,
      kembalian,
      'selesai',
      '',
      new Date()
    ]);

    // Save items to penjualan_item sheet
    const penjualanItemSheet = getSheet(CONFIG.SHEETS.PENJUALAN_ITEM);

    for (let item of transactionData.items) {
      const itemId = getNextId(CONFIG.SHEETS.PENJUALAN_ITEM);
      const subtotalItem = item.harga_jual * item.quantity;

      penjualanItemSheet.appendRow([
        itemId,
        penjualanId,
        item.barang_id || null,
        item.kode_barang,
        item.nama_barang,
        item.harga_jual,
        item.quantity,
        0, // diskon_item_persen
        0, // diskon_item_nominal
        subtotalItem,
        new Date()
      ]);

      // Update stock
      updateStock(item.kode_barang, -item.quantity);
    }

    return {
      success: true,
      message: 'Transaksi berhasil',
      transaction: {
        id: penjualanId,
        nomor_transaksi: nomorTransaksi,
        total_bayar: totalBayar,
        kembalian: kembalian,
        created_at: new Date()
      }
    };

  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function generateTransactionNumber() {
  const now = new Date();
  const year = now.getFullYear().toString().substr(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const date = now.getDate().toString().padStart(2, '0');
  const time = now.getTime().toString().substr(-6);

  return `TRX${year}${month}${date}${time}`;
}

function updateStock(kodeBarang, quantityChange) {
  try {
    const sheet = getSheet(CONFIG.SHEETS.BARANG);
    const data = sheet.getDataRange().getValues();

    for (let i = 1; i < data.length; i++) {
      if (data[i][1] === kodeBarang) {
        const currentStock = data[i][8] || 0;
        const newStock = currentStock + quantityChange;
        sheet.getRange(i + 1, 9).setValue(Math.max(0, newStock));
        sheet.getRange(i + 1, 13).setValue(new Date()); // updated_at
        break;
      }
    }
  } catch (e) {
    console.error('Error updating stock:', e);
  }
}

// ===== INITIALIZATION FUNCTIONS =====
function initializeDefaultData() {
  try {
    // Create default admin user
    const userSheet = getSheet(CONFIG.SHEETS.USERS);
    const userData = userSheet.getDataRange().getValues();

    if (userData.length <= 1) { // Only header row
      userSheet.appendRow([1, 'admin', 'admin123', 'Administrator', 'administrator', 'aktif', new Date(), null]);
      userSheet.appendRow([2, 'kasir', 'kasir123', 'Kasir', 'kasir', 'aktif', new Date(), null]);
    }

    // Create default categories
    const kategoriSheet = getSheet(CONFIG.SHEETS.KATEGORI);
    const kategoriData = kategoriSheet.getDataRange().getValues();

    if (kategoriData.length <= 1) {
      kategoriSheet.appendRow([1, 'Makanan', 'Kategori makanan dan minuman', 'aktif', new Date()]);
      kategoriSheet.appendRow([2, 'Elektronik', 'Kategori barang elektronik', 'aktif', new Date()]);
      kategoriSheet.appendRow([3, 'Pakaian', 'Kategori pakaian dan aksesoris', 'aktif', new Date()]);
    }

    // Create default units
    const satuanSheet = getSheet(CONFIG.SHEETS.SATUAN);
    const satuanData = satuanSheet.getDataRange().getValues();

    if (satuanData.length <= 1) {
      satuanSheet.appendRow([1, 'Pieces', 'pcs', 'aktif', new Date()]);
      satuanSheet.appendRow([2, 'Kilogram', 'kg', 'aktif', new Date()]);
      satuanSheet.appendRow([3, 'Liter', 'L', 'aktif', new Date()]);
      satuanSheet.appendRow([4, 'Meter', 'm', 'aktif', new Date()]);
    }

    return { success: true, message: 'Data default berhasil dibuat' };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

// ===== REPORT FUNCTIONS =====
function generateReport(params) {
  try {
    const startDate = new Date(params.startDate);
    const endDate = new Date(params.endDate);
    endDate.setHours(23, 59, 59, 999); // End of day

    let reportData = {};

    switch (params.reportType) {
      case 'penjualan':
        reportData = generateSalesReport(startDate, endDate);
        break;
      case 'produk':
        reportData = generateProductReport(startDate, endDate);
        break;
      case 'stok':
        reportData = generateStockReport();
        break;
      default:
        return { success: false, message: 'Jenis laporan tidak dikenali' };
    }

    return { success: true, data: reportData };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function generateSalesReport(startDate, endDate) {
  const penjualanSheet = getSheet(CONFIG.SHEETS.PENJUALAN);
  const penjualanData = penjualanSheet.getDataRange().getValues();
  const userSheet = getSheet(CONFIG.SHEETS.USERS);
  const userData = userSheet.getDataRange().getValues();
  const pelangganSheet = getSheet(CONFIG.SHEETS.PELANGGAN);
  const pelangganData = pelangganSheet.getDataRange().getValues();

  let totalTransaksi = 0;
  let totalPendapatan = 0;
  let totalItem = 0;
  const tableData = [];
  const dailySales = {};

  for (let i = 1; i < penjualanData.length; i++) {
    const row = penjualanData[i];
    const transactionDate = new Date(row[2]);

    if (transactionDate >= startDate && transactionDate <= endDate && row[16] === 'selesai') {
      totalTransaksi++;
      totalPendapatan += row[10] || 0;

      // Get kasir name
      const kasir = userData.find(u => u[0] == row[3]);
      const kasirName = kasir ? kasir[3] : 'Unknown';

      // Get pelanggan name
      const pelanggan = pelangganData.find(p => p[0] == row[4]);
      const pelangganName = pelanggan ? pelanggan[2] : 'Umum';

      tableData.push([
        row[1], // nomor_transaksi
        Utilities.formatDate(transactionDate, 'GMT+7', 'dd/MM/yyyy HH:mm'),
        kasirName,
        pelangganName,
        'Rp ' + new Intl.NumberFormat('id-ID').format(row[10]),
        row[16]
      ]);

      // Daily sales for chart
      const dateKey = Utilities.formatDate(transactionDate, 'GMT+7', 'dd/MM');
      dailySales[dateKey] = (dailySales[dateKey] || 0) + (row[10] || 0);
    }
  }

  const rataRata = totalTransaksi > 0 ? totalPendapatan / totalTransaksi : 0;

  return {
    summary: {
      totalTransaksi: totalTransaksi,
      totalPendapatan: totalPendapatan,
      totalItem: totalItem,
      rataRata: rataRata
    },
    chartData: {
      salesLabels: Object.keys(dailySales),
      salesData: Object.values(dailySales),
      productLabels: [],
      productData: []
    },
    tableData: tableData
  };
}

function generateProductReport(startDate, endDate) {
  const penjualanItemSheet = getSheet(CONFIG.SHEETS.PENJUALAN_ITEM);
  const penjualanItemData = penjualanItemSheet.getDataRange().getValues();
  const penjualanSheet = getSheet(CONFIG.SHEETS.PENJUALAN);
  const penjualanData = penjualanSheet.getDataRange().getValues();
  const barangSheet = getSheet(CONFIG.SHEETS.BARANG);
  const barangData = barangSheet.getDataRange().getValues();

  const productSales = {};
  let totalItem = 0;

  // Get valid transaction IDs within date range
  const validTransactionIds = new Set();
  for (let i = 1; i < penjualanData.length; i++) {
    const transactionDate = new Date(penjualanData[i][2]);
    if (transactionDate >= startDate && transactionDate <= endDate && penjualanData[i][16] === 'selesai') {
      validTransactionIds.add(penjualanData[i][0]);
    }
  }

  // Aggregate product sales
  for (let i = 1; i < penjualanItemData.length; i++) {
    const row = penjualanItemData[i];
    const penjualanId = row[1];

    if (validTransactionIds.has(penjualanId)) {
      const kodeBarang = row[3];
      const namaBarang = row[4];
      const quantity = row[6] || 0;
      const subtotal = row[9] || 0;

      if (!productSales[kodeBarang]) {
        productSales[kodeBarang] = {
          kode: kodeBarang,
          nama: namaBarang,
          quantity: 0,
          pendapatan: 0,
          kategori: ''
        };
      }

      productSales[kodeBarang].quantity += quantity;
      productSales[kodeBarang].pendapatan += subtotal;
      totalItem += quantity;
    }
  }

  // Add category info
  for (let kode in productSales) {
    const barang = barangData.find(b => b[1] === kode);
    if (barang) {
      // Get category name (would need to join with kategori table)
      productSales[kode].kategori = 'Kategori'; // Simplified
    }
  }

  const tableData = Object.values(productSales).map(product => [
    product.kode,
    product.nama,
    product.kategori,
    product.quantity,
    'Rp ' + new Intl.NumberFormat('id-ID').format(product.pendapatan)
  ]);

  // Top 5 products for chart
  const sortedProducts = Object.values(productSales).sort((a, b) => b.pendapatan - a.pendapatan).slice(0, 5);

  return {
    summary: {
      totalTransaksi: validTransactionIds.size,
      totalPendapatan: Object.values(productSales).reduce((sum, p) => sum + p.pendapatan, 0),
      totalItem: totalItem,
      rataRata: 0
    },
    chartData: {
      salesLabels: [],
      salesData: [],
      productLabels: sortedProducts.map(p => p.nama),
      productData: sortedProducts.map(p => p.pendapatan)
    },
    tableData: tableData
  };
}

function generateStockReport() {
  const barangSheet = getSheet(CONFIG.SHEETS.BARANG);
  const barangData = barangSheet.getDataRange().getValues();

  const tableData = [];

  for (let i = 1; i < barangData.length; i++) {
    const row = barangData[i];
    if (row[0]) {
      const stokStatus = row[8] <= row[7] ? 'Stok Menipis' : 'Stok Aman';

      tableData.push([
        row[1], // kode_barang
        row[2], // nama_barang
        row[8], // stok_saat_ini
        row[7], // stok_minimum
        stokStatus
      ]);
    }
  }

  return {
    summary: {
      totalTransaksi: 0,
      totalPendapatan: 0,
      totalItem: tableData.length,
      rataRata: 0
    },
    chartData: {
      salesLabels: [],
      salesData: [],
      productLabels: [],
      productData: []
    },
    tableData: tableData
  };
}
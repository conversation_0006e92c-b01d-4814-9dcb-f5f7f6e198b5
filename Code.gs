// ===== POS SYSTEM - SIMPLE VERSION =====

// Configuration
const SPREADSHEET_NAME = 'pos_simple';

// ===== MAIN FUNCTIONS =====
function doGet(e) {
  const page = e.parameter.page || 'login';
  
  // Check if user is logged in (except for login page)
  if (page !== 'login') {
    const user = getUser();
    if (!user) {
      return createPage('login');
    }
  }
  
  // Route to appropriate page
  switch(page) {
    case 'login':
      return createPage('login');
    case 'dashboard':
      return createPage('dashboard');
    case 'kasir':
      return createPage('kasir');
    case 'barang':
      return createPage('barang');
    case 'laporan':
      return createPage('laporan');
    default:
      return createPage('login');
  }
}

function createPage(pageName) {
  const template = HtmlService.createTemplateFromFile(pageName);
  template.user = getUser();
  return template.evaluate()
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
    .setTitle('POS System');
}

// ===== AUTHENTICATION =====
function login(username, password) {
  try {
    // Get or create spreadsheet
    const ss = getSpreadsheet();
    const sheet = getOrCreateSheet(ss, 'users');

    // Initialize users if empty
    if (sheet.getLastRow() <= 1) {
      initializeUsers(sheet);
    }

    // Check login - SIMPLE VERSION (Plain Text)
    const data = sheet.getDataRange().getValues();
    for (let i = 1; i < data.length; i++) {
      if (data[i][1] === username && data[i][2] === password && data[i][4] === 'aktif') {
        const user = {
          id: data[i][0],
          username: data[i][1],
          nama: data[i][3],
          role: data[i][4]
        };

        // Save session
        PropertiesService.getScriptProperties().setProperty('user', JSON.stringify(user));

        return { success: true, user: user };
      }
    }

    return { success: false, message: 'Username atau password salah' };
  } catch (e) {
    return { success: false, message: 'Error: ' + e.toString() };
  }
}

// ===== PASSWORD HASHING (Optional - Uncomment to use) =====
/*
function hashPassword(password) {
  return Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, password)
    .map(function(byte) {
      return ('0' + (byte & 0xFF).toString(16)).slice(-2);
    }).join('');
}

function loginWithHash(username, password) {
  try {
    const ss = getSpreadsheet();
    const sheet = getOrCreateSheet(ss, 'users');

    if (sheet.getLastRow() <= 1) {
      initializeUsersWithHash(sheet);
    }

    const hashedPassword = hashPassword(password);
    const data = sheet.getDataRange().getValues();

    for (let i = 1; i < data.length; i++) {
      if (data[i][1] === username && data[i][2] === hashedPassword && data[i][4] === 'aktif') {
        const user = {
          id: data[i][0],
          username: data[i][1],
          nama: data[i][3],
          role: data[i][4]
        };

        PropertiesService.getScriptProperties().setProperty('user', JSON.stringify(user));
        return { success: true, user: user };
      }
    }

    return { success: false, message: 'Username atau password salah' };
  } catch (e) {
    return { success: false, message: 'Error: ' + e.toString() };
  }
}

function initializeUsersWithHash(sheet) {
  sheet.getRange(1, 1, 1, 5).setValues([
    ['id', 'username', 'password_hash', 'nama', 'status']
  ]);

  sheet.getRange(2, 1, 2, 5).setValues([
    [1, 'admin', hashPassword('admin123'), 'Administrator', 'aktif'],
    [2, 'kasir', hashPassword('kasir123'), 'Kasir', 'aktif']
  ]);
}
*/

function logout() {
  PropertiesService.getScriptProperties().deleteProperty('user');
  return { success: true };
}

function getUser() {
  try {
    const userStr = PropertiesService.getScriptProperties().getProperty('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (e) {
    return null;
  }
}

// ===== SPREADSHEET FUNCTIONS =====
function getSpreadsheet() {
  try {
    // Try to find existing spreadsheet
    const files = DriveApp.getFilesByName(SPREADSHEET_NAME);
    if (files.hasNext()) {
      return SpreadsheetApp.openById(files.next().getId());
    }
    
    // Create new spreadsheet
    return SpreadsheetApp.create(SPREADSHEET_NAME);
  } catch (e) {
    throw new Error('Cannot access spreadsheet: ' + e.toString());
  }
}

function getOrCreateSheet(ss, sheetName) {
  let sheet = ss.getSheetByName(sheetName);
  if (!sheet) {
    sheet = ss.insertSheet(sheetName);
  }
  return sheet;
}

function initializeUsers(sheet) {
  // Add headers
  sheet.getRange(1, 1, 1, 5).setValues([
    ['id', 'username', 'password', 'nama', 'status']
  ]);
  
  // Add default users
  sheet.getRange(2, 1, 2, 5).setValues([
    [1, 'admin', 'admin123', 'Administrator', 'aktif'],
    [2, 'kasir', 'kasir123', 'Kasir', 'aktif']
  ]);
}

// ===== BARANG FUNCTIONS =====
function getBarangList() {
  try {
    const ss = getSpreadsheet();
    const sheet = getOrCreateSheet(ss, 'barang');
    
    // Initialize if empty
    if (sheet.getLastRow() <= 1) {
      initializeBarang(sheet);
    }
    
    const data = sheet.getDataRange().getValues();
    const result = [];
    
    for (let i = 1; i < data.length; i++) {
      if (data[i][0]) {
        result.push({
          id: data[i][0],
          kode: data[i][1],
          nama: data[i][2],
          harga: data[i][3],
          stok: data[i][4],
          status: data[i][5]
        });
      }
    }
    
    return { success: true, data: result };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function saveBarang(data) {
  try {
    const ss = getSpreadsheet();
    const sheet = getOrCreateSheet(ss, 'barang');
    
    if (data.id) {
      // Update existing
      const rows = sheet.getDataRange().getValues();
      for (let i = 1; i < rows.length; i++) {
        if (rows[i][0] == data.id) {
          sheet.getRange(i + 1, 1, 1, 6).setValues([[
            data.id, data.kode, data.nama, data.harga, data.stok, data.status
          ]]);
          return { success: true, message: 'Barang berhasil diupdate' };
        }
      }
    } else {
      // Add new
      const newId = sheet.getLastRow();
      sheet.appendRow([newId, data.kode, data.nama, data.harga, data.stok, data.status || 'aktif']);
      return { success: true, message: 'Barang berhasil ditambah' };
    }
    
    return { success: false, message: 'Data tidak ditemukan' };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function deleteBarang(id) {
  try {
    const ss = getSpreadsheet();
    const sheet = getOrCreateSheet(ss, 'barang');
    const rows = sheet.getDataRange().getValues();
    
    for (let i = 1; i < rows.length; i++) {
      if (rows[i][0] == id) {
        sheet.deleteRow(i + 1);
        return { success: true, message: 'Barang berhasil dihapus' };
      }
    }
    
    return { success: false, message: 'Barang tidak ditemukan' };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function initializeBarang(sheet) {
  // Add headers
  sheet.getRange(1, 1, 1, 6).setValues([
    ['id', 'kode', 'nama', 'harga', 'stok', 'status']
  ]);
  
  // Add sample data
  sheet.getRange(2, 1, 3, 6).setValues([
    [1, 'BRG001', 'Nasi Gudeg', 15000, 50, 'aktif'],
    [2, 'BRG002', 'Es Teh Manis', 5000, 100, 'aktif'],
    [3, 'BRG003', 'Ayam Bakar', 25000, 30, 'aktif']
  ]);
}

// ===== TRANSACTION FUNCTIONS =====
function processTransaction(items, total, bayar) {
  try {
    const ss = getSpreadsheet();
    const sheet = getOrCreateSheet(ss, 'transaksi');
    
    // Initialize if empty
    if (sheet.getLastRow() <= 1) {
      sheet.getRange(1, 1, 1, 7).setValues([
        ['id', 'tanggal', 'items', 'total', 'bayar', 'kembalian', 'kasir']
      ]);
    }
    
    const user = getUser();
    const kembalian = bayar - total;
    const transaksiId = 'TRX' + Date.now();
    
    // Save transaction
    sheet.appendRow([
      transaksiId,
      new Date(),
      JSON.stringify(items),
      total,
      bayar,
      kembalian,
      user.nama
    ]);
    
    // Update stock
    updateStock(items);
    
    return {
      success: true,
      transaksi: {
        id: transaksiId,
        total: total,
        bayar: bayar,
        kembalian: kembalian
      }
    };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

function updateStock(items) {
  try {
    const ss = getSpreadsheet();
    const sheet = getOrCreateSheet(ss, 'barang');
    const data = sheet.getDataRange().getValues();
    
    items.forEach(item => {
      for (let i = 1; i < data.length; i++) {
        if (data[i][0] == item.id) {
          const currentStock = data[i][4];
          const newStock = currentStock - item.qty;
          sheet.getRange(i + 1, 5).setValue(Math.max(0, newStock));
          break;
        }
      }
    });
  } catch (e) {
    console.error('Error updating stock:', e);
  }
}

// ===== REPORT FUNCTIONS =====
function getTransaksiList() {
  try {
    const ss = getSpreadsheet();
    const sheet = getOrCreateSheet(ss, 'transaksi');
    
    if (sheet.getLastRow() <= 1) {
      return { success: true, data: [] };
    }
    
    const data = sheet.getDataRange().getValues();
    const result = [];
    
    for (let i = 1; i < data.length; i++) {
      if (data[i][0]) {
        result.push({
          id: data[i][0],
          tanggal: data[i][1],
          total: data[i][3],
          bayar: data[i][4],
          kembalian: data[i][5],
          kasir: data[i][6]
        });
      }
    }
    
    return { success: true, data: result.reverse() };
  } catch (e) {
    return { success: false, message: e.toString() };
  }
}

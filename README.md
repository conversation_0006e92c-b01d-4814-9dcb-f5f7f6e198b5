# POS System - Point of Sale Application

Aplikasi Point of Sale (POS) berbasis Google Apps Script dengan interface AdminLTE yang modern dan responsif.

## 🚀 Fitur Utama

### 💰 Transaksi Penjualan
- Interface kasir yang user-friendly
- Scanner barcode (simulasi)
- Multiple payment methods (Cash, Card, Digital Wallet)
- Kombinasi pembayaran
- Kalkulasi otomatis pajak dan diskon
- Print receipt

### 📦 Manajemen Produk
- CRUD data barang lengkap
- Kategori dan satuan produk
- Tracking stok real-time
- Alert stok menipis
- **Import/Export Excel (.xlsx)**
- Upload gambar produk

### 👥 Manajemen Pelanggan
- Data pelanggan member dan reguler
- Sistem poin reward
- Diskon khusus member

### 📊 Laporan & Analytics
- Dashboard dengan statistik real-time
- Laporan penjualan harian/bulanan
- Top selling products
- Grafik penjualan interaktif
- Export laporan ke Excel

### 🔐 User Management
- Role-based access (Administrator & Kasir)
- Session management
- Login/logout system

## 🛠️ Teknologi

- **Backend**: Google Apps Script
- **Database**: Google Sheets
- **Frontend**: HTML, CSS, JavaScript
- **UI Framework**: AdminLTE 3.2.0
- **Charts**: Chart.js
- **Icons**: Font Awesome 6.4.0
- **Components**: Bootstrap 4, jQuery, Select2, DataTables

## 📋 Struktur Database

### Sheet: users
- id, username, password, nama_lengkap, role, status, created_at, last_login

### Sheet: kategori
- id, nama_kategori, deskripsi, status, created_at

### Sheet: satuan
- id, nama_satuan, singkatan, status, created_at

### Sheet: barang
- id, kode_barang, nama_barang, kategori_id, satuan_id, harga_beli, harga_jual, stok_minimum, stok_saat_ini, gambar_url, status, created_at, updated_at

### Sheet: pelanggan
- id, kode_pelanggan, nama_pelanggan, telepon, email, alamat, tipe, diskon_persen, total_pembelian, poin_reward, status, created_at

### Sheet: penjualan
- id, nomor_transaksi, tanggal_transaksi, kasir_id, pelanggan_id, subtotal, diskon_persen, diskon_nominal, pajak_persen, pajak_nominal, total_bayar, metode_pembayaran, cash_amount, card_amount, digital_amount, kembalian, status, catatan, created_at

### Sheet: penjualan_item
- id, penjualan_id, barang_id, kode_barang, nama_barang, harga_satuan, quantity, diskon_item_persen, diskon_item_nominal, subtotal_item, created_at

## 🚀 Instalasi

### 1. Persiapan Google Sheets
1. Buat Google Sheets baru dengan nama "pos"
2. Rename sheet-sheet sesuai struktur database:
   - Sheet1 → users
   - Sheet2 → barang  
   - Sheet3 → kategori
   - Sheet4 → satuan
   - Sheet5 → penjualan
   - Sheet6 → penjualan_item
   - Sheet7 → pelanggan

### 2. Setup Google Apps Script
1. Buka [script.google.com](https://script.google.com)
2. Buat project baru
3. Copy-paste semua file dari repository ini:
   - Code.gs (main backend file)
   - header.html
   - navbar.html
   - sidebar.html
   - footer.html
   - login.html
   - index.html
   - kasir.html
   - barang.html
   - kategori.html
   - satuan.html
   - laporan.html

### 3. Deploy sebagai Web App
1. Klik "Deploy" → "New deployment"
2. Pilih type: "Web app"
3. Execute as: "Me"
4. Who has access: "Anyone" atau sesuai kebutuhan
5. Klik "Deploy"
6. Copy URL web app

### 4. Inisialisasi Data
1. Jalankan fungsi `initializeDefaultData()` di Apps Script Editor
2. Atau login dengan akun default dan sistem akan otomatis membuat data awal

## 👤 Akun Default

### Administrator
- **Username**: admin
- **Password**: admin123
- **Akses**: Full access ke semua fitur

### Kasir
- **Username**: kasir  
- **Password**: kasir123
- **Akses**: Kasir, lihat data master, laporan terbatas

## 📱 Penggunaan

### Kasir
1. Login ke sistem
2. Pilih menu "Kasir"
3. Scan barcode atau pilih produk
4. Atur quantity dan diskon
5. Pilih pelanggan (opsional)
6. Pilih metode pembayaran
7. Proses pembayaran
8. Print receipt

### Manajemen Barang
1. Menu "Master Data" → "Data Barang"
2. Klik "Tambah Barang" untuk input manual
3. Atau gunakan "Import Excel" untuk bulk upload
4. Download template Excel terlebih dahulu
5. Export data ke Excel kapan saja

### Laporan
1. Menu "Laporan"
2. Pilih rentang tanggal
3. Pilih jenis laporan
4. Klik "Generate Laporan"
5. Lihat grafik dan tabel data
6. Export ke Excel atau print

## 🔧 Kustomisasi

### Menambah Field Baru
1. Edit struktur di `initializeSheet()` function
2. Update CRUD functions terkait
3. Modifikasi form HTML
4. Update JavaScript handlers

### Mengubah Tampilan
1. Edit file header.html untuk CSS custom
2. Modifikasi navbar.html dan sidebar.html untuk menu
3. Update footer.html untuk JavaScript tambahan

### Integrasi API External
1. Tambahkan fungsi di Code.gs
2. Gunakan `UrlFetchApp` untuk HTTP requests
3. Handle response dan error appropriately

## 🐛 Troubleshooting

### Error "Spreadsheet not found"
- Pastikan nama spreadsheet adalah "pos"
- Pastikan spreadsheet dapat diakses oleh script

### Login tidak berhasil
- Cek data di sheet "users"
- Pastikan status user "aktif"
- Clear browser cache

### Import Excel gagal
- Pastikan format file .xlsx
- Cek template header sesuai
- Pastikan data tidak ada yang kosong untuk field wajib

### Barcode scanner tidak berfungsi
- Fitur ini adalah simulasi untuk demo
- Untuk implementasi real, perlu integrasi dengan hardware scanner

## 📄 Lisensi

MIT License - Silakan gunakan dan modifikasi sesuai kebutuhan.

## 🤝 Kontribusi

1. Fork repository
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Buat Pull Request

## 📞 Support

Untuk pertanyaan dan support, silakan buat issue di repository ini.

---

**Dibuat dengan ❤️ menggunakan Google Apps Script dan AdminLTE**

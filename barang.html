<?!= include('header') ?>

<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <?!= include('navbar') ?>
  <?!= include('sidebar') ?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper" style="margin-left: 250px !important; padding-top: 60px;">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Data Barang</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="?page=index">Home</a></li>
              <li class="breadcrumb-item"><a href="#">Master Data</a></li>
              <li class="breadcrumb-item active">Data Barang</li>
            </ol>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <!-- Import/Export Buttons -->
        <div class="row import-export-buttons">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-file-excel mr-1"></i>
                  Import/Export Data
                </h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3">
                    <button class="btn btn-success btn-block" onclick="downloadTemplate()">
                      <i class="fas fa-download"></i> Download Template
                    </button>
                  </div>
                  <div class="col-md-3">
                    <button class="btn btn-primary btn-block" onclick="showImportModal()">
                      <i class="fas fa-upload"></i> Import Excel
                    </button>
                  </div>
                  <div class="col-md-3">
                    <button class="btn btn-info btn-block" onclick="exportToExcel()">
                      <i class="fas fa-file-export"></i> Export Excel
                    </button>
                  </div>
                  <div class="col-md-3">
                    <button class="btn btn-warning btn-block" onclick="showAddModal()">
                      <i class="fas fa-plus"></i> Tambah Barang
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Data Table -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-table mr-1"></i>
                  Daftar Barang
                </h3>
                <div class="card-tools">
                  <div class="input-group input-group-sm" style="width: 250px;">
                    <input type="text" name="table_search" class="form-control float-right" placeholder="Cari barang..." id="search-input">
                    <div class="input-group-append">
                      <button type="submit" class="btn btn-default">
                        <i class="fas fa-search"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap" id="barang-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Kode Barang</th>
                      <th>Nama Barang</th>
                      <th>Kategori</th>
                      <th>Satuan</th>
                      <th>Harga Beli</th>
                      <th>Harga Jual</th>
                      <th>Stok</th>
                      <th>Status</th>
                      <th>Aksi</th>
                    </tr>
                  </thead>
                  <tbody id="barang-tbody">
                    <tr>
                      <td colspan="10" class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Memuat data...
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- Add/Edit Modal -->
  <div class="modal fade" id="barang-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title" id="modal-title">Tambah Barang</h4>
          <button type="button" class="close" data-dismiss="modal">
            <span>&times;</span>
          </button>
        </div>
        <form id="barang-form">
          <div class="modal-body">
            <input type="hidden" id="barang-id">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="kode-barang">Kode Barang *</label>
                  <input type="text" class="form-control" id="kode-barang" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="nama-barang">Nama Barang *</label>
                  <input type="text" class="form-control" id="nama-barang" required>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="kategori-id">Kategori *</label>
                  <select class="form-control select2" id="kategori-id" required>
                    <option value="">Pilih Kategori</option>
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="satuan-id">Satuan *</label>
                  <select class="form-control select2" id="satuan-id" required>
                    <option value="">Pilih Satuan</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="harga-beli">Harga Beli *</label>
                  <input type="text" class="form-control currency" id="harga-beli" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="harga-jual">Harga Jual *</label>
                  <input type="text" class="form-control currency" id="harga-jual" required>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="stok-minimum">Stok Minimum</label>
                  <input type="number" class="form-control" id="stok-minimum" value="0">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="stok-saat-ini">Stok Saat Ini</label>
                  <input type="number" class="form-control" id="stok-saat-ini" value="0">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="status">Status</label>
                  <select class="form-control" id="status">
                    <option value="aktif">Aktif</option>
                    <option value="nonaktif">Non Aktif</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="gambar-url">URL Gambar</label>
              <input type="url" class="form-control" id="gambar-url" placeholder="https://example.com/image.jpg">
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
            <button type="submit" class="btn btn-primary">Simpan</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Import Modal -->
  <div class="modal fade" id="import-modal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">Import Data Barang</h4>
          <button type="button" class="close" data-dismiss="modal">
            <span>&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="file-upload-area" id="file-upload-area">
            <i class="fas fa-cloud-upload-alt fa-3x mb-3"></i>
            <h5>Drag & Drop file Excel di sini</h5>
            <p>atau klik untuk memilih file</p>
            <input type="file" id="excel-file" accept=".xlsx,.xls" style="display: none;">
          </div>
          <div class="mt-3" id="file-info" style="display: none;">
            <div class="alert alert-info">
              <strong>File dipilih:</strong> <span id="file-name"></span><br>
              <strong>Ukuran:</strong> <span id="file-size"></span>
            </div>
          </div>
          <div class="mt-3" id="import-progress" style="display: none;">
            <div class="progress">
              <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small class="text-muted">Mengimpor data...</small>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
          <button type="button" class="btn btn-primary" id="import-btn" onclick="importExcel()" disabled>Import</button>
        </div>
      </div>
    </div>
  </div>

  <?!= include('footer') ?>

  <script>
  let barangData = [];
  let kategoriData = [];
  let satuanData = [];
  let selectedFile = null;

  $(document).ready(function() {
    loadInitialData();
    setupEventHandlers();
  });

  function loadInitialData() {
    loadBarangData();
    loadKategoriData();
    loadSatuanData();
  }

  function setupEventHandlers() {
    // Search functionality
    $('#search-input').on('keyup', function() {
      const searchTerm = $(this).val().toLowerCase();
      filterTable(searchTerm);
    });

    // Form submission
    $('#barang-form').on('submit', function(e) {
      e.preventDefault();
      saveBarang();
    });

    // File upload
    $('#file-upload-area').on('click', function() {
      $('#excel-file').click();
    });

    $('#excel-file').on('change', function() {
      handleFileSelect(this.files[0]);
    });

    // Drag and drop
    $('#file-upload-area').on('dragover', function(e) {
      e.preventDefault();
      $(this).addClass('dragover');
    });

    $('#file-upload-area').on('dragleave', function(e) {
      e.preventDefault();
      $(this).removeClass('dragover');
    });

    $('#file-upload-area').on('drop', function(e) {
      e.preventDefault();
      $(this).removeClass('dragover');
      const files = e.originalEvent.dataTransfer.files;
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    });
  }

  function loadBarangData() {
    google.script.run
      .withSuccessHandler(function(result) {
        if (result.success) {
          barangData = result.data;
          displayBarangData(barangData);
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showError('Error loading data: ' + error.toString());
      })
      .getBarangList();
  }

  function loadKategoriData() {
    google.script.run
      .withSuccessHandler(function(result) {
        if (result.success) {
          kategoriData = result.data;
          populateKategoriSelect();
        }
      })
      .getKategoriList();
  }

  function loadSatuanData() {
    google.script.run
      .withSuccessHandler(function(result) {
        if (result.success) {
          satuanData = result.data;
          populateSatuanSelect();
        }
      })
      .getSatuanList();
  }

  function displayBarangData(data) {
    const tbody = $('#barang-tbody');
    tbody.empty();

    if (data.length === 0) {
      tbody.append('<tr><td colspan="10" class="text-center text-muted">Tidak ada data</td></tr>');
      return;
    }

    data.forEach(item => {
      const statusBadge = item.status === 'aktif' ? 'badge-success' : 'badge-secondary';
      const stokBadge = item.stok_saat_ini <= item.stok_minimum ? 'badge-danger' : 'badge-success';
      
      tbody.append(`
        <tr>
          <td>${item.id}</td>
          <td>${item.kode_barang}</td>
          <td>${item.nama_barang}</td>
          <td>${item.kategori_nama || '-'}</td>
          <td>${item.satuan_nama || '-'}</td>
          <td>${formatCurrency(item.harga_beli)}</td>
          <td>${formatCurrency(item.harga_jual)}</td>
          <td><span class="badge ${stokBadge}">${item.stok_saat_ini}</span></td>
          <td><span class="badge ${statusBadge}">${item.status}</span></td>
          <td>
            <div class="btn-group btn-group-sm">
              <button class="btn btn-info" onclick="editBarang(${item.id})" title="Edit">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn btn-danger" onclick="deleteBarang(${item.id})" title="Hapus">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      `);
    });
  }

  function populateKategoriSelect() {
    const select = $('#kategori-id');
    select.empty().append('<option value="">Pilih Kategori</option>');
    
    kategoriData.forEach(item => {
      if (item.status === 'aktif') {
        select.append(`<option value="${item.id}">${item.nama_kategori}</option>`);
      }
    });
  }

  function populateSatuanSelect() {
    const select = $('#satuan-id');
    select.empty().append('<option value="">Pilih Satuan</option>');
    
    satuanData.forEach(item => {
      if (item.status === 'aktif') {
        select.append(`<option value="${item.id}">${item.nama_satuan}</option>`);
      }
    });
  }

  function filterTable(searchTerm) {
    const filtered = barangData.filter(item => 
      item.kode_barang.toLowerCase().includes(searchTerm) ||
      item.nama_barang.toLowerCase().includes(searchTerm) ||
      (item.kategori_nama && item.kategori_nama.toLowerCase().includes(searchTerm))
    );
    displayBarangData(filtered);
  }

  function showAddModal() {
    $('#modal-title').text('Tambah Barang');
    $('#barang-form')[0].reset();
    $('#barang-id').val('');
    $('#barang-modal').modal('show');
  }

  function editBarang(id) {
    const item = barangData.find(b => b.id === id);
    if (!item) return;

    $('#modal-title').text('Edit Barang');
    $('#barang-id').val(item.id);
    $('#kode-barang').val(item.kode_barang);
    $('#nama-barang').val(item.nama_barang);
    $('#kategori-id').val(item.kategori_id);
    $('#satuan-id').val(item.satuan_id);
    $('#harga-beli').val(item.harga_beli);
    $('#harga-jual').val(item.harga_jual);
    $('#stok-minimum').val(item.stok_minimum);
    $('#stok-saat-ini').val(item.stok_saat_ini);
    $('#gambar-url').val(item.gambar_url);
    $('#status').val(item.status);
    
    $('#barang-modal').modal('show');
  }

  function saveBarang() {
    const data = {
      id: $('#barang-id').val() || null,
      kode_barang: $('#kode-barang').val(),
      nama_barang: $('#nama-barang').val(),
      kategori_id: $('#kategori-id').val(),
      satuan_id: $('#satuan-id').val(),
      harga_beli: parseFloat($('#harga-beli').val().replace(/[^\d]/g, '')),
      harga_jual: parseFloat($('#harga-jual').val().replace(/[^\d]/g, '')),
      stok_minimum: parseInt($('#stok-minimum').val()) || 0,
      stok_saat_ini: parseInt($('#stok-saat-ini').val()) || 0,
      gambar_url: $('#gambar-url').val(),
      status: $('#status').val()
    };

    showLoading(true);
    google.script.run
      .withSuccessHandler(function(result) {
        showLoading(false);
        if (result.success) {
          showSuccess(result.message);
          $('#barang-modal').modal('hide');
          loadBarangData();
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
      })
      .saveBarang(data);
  }

  function deleteBarang(id) {
    const item = barangData.find(b => b.id === id);
    if (!item) return;

    Swal.fire({
      title: 'Hapus Barang?',
      text: `Apakah Anda yakin ingin menghapus "${item.nama_barang}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Ya, Hapus',
      cancelButtonText: 'Batal'
    }).then((result) => {
      if (result.isConfirmed) {
        google.script.run
          .withSuccessHandler(function(result) {
            if (result.success) {
              showSuccess(result.message);
              loadBarangData();
            } else {
              showError(result.message);
            }
          })
          .withFailureHandler(function(error) {
            showError('Error: ' + error.toString());
          })
          .deleteBarang(id);
      }
    });
  }

  function downloadTemplate() {
    showLoading(true);
    google.script.run
      .withSuccessHandler(function(result) {
        showLoading(false);
        if (result.success) {
          window.open(result.url, '_blank');
          showSuccess('Template berhasil dibuat');
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
      })
      .getImportTemplate();
  }

  function exportToExcel() {
    showLoading(true);
    google.script.run
      .withSuccessHandler(function(result) {
        showLoading(false);
        if (result.success) {
          window.open(result.url, '_blank');
          showSuccess('Export berhasil');
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
      })
      .exportBarangToXLSX();
  }

  function showImportModal() {
    $('#import-modal').modal('show');
    resetImportModal();
  }

  function resetImportModal() {
    selectedFile = null;
    $('#file-info').hide();
    $('#import-progress').hide();
    $('#import-btn').prop('disabled', true);
    $('#file-upload-area').removeClass('dragover');
  }

  function handleFileSelect(file) {
    if (!file) return;

    if (!file.name.match(/\.(xlsx|xls)$/)) {
      showError('Hanya file Excel (.xlsx, .xls) yang diperbolehkan');
      return;
    }

    selectedFile = file;
    $('#file-name').text(file.name);
    $('#file-size').text(formatFileSize(file.size));
    $('#file-info').show();
    $('#import-btn').prop('disabled', false);
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function importExcel() {
    if (!selectedFile) {
      showError('Pilih file terlebih dahulu');
      return;
    }

    $('#import-progress').show();
    const progressBar = $('.progress-bar');
    
    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      progressBar.css('width', progress + '%');
      if (progress >= 90) {
        clearInterval(interval);
      }
    }, 100);

    // Read file and process
    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, {type: 'array'});
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Process import
        google.script.run
          .withSuccessHandler(function(result) {
            clearInterval(interval);
            progressBar.css('width', '100%');
            
            setTimeout(() => {
              $('#import-modal').modal('hide');
              if (result.success) {
                showSuccess(result.message);
                if (result.errors && result.errors.length > 0) {
                  console.log('Import errors:', result.errors);
                }
                loadBarangData();
              } else {
                showError(result.message);
              }
            }, 500);
          })
          .withFailureHandler(function(error) {
            clearInterval(interval);
            showError('Error: ' + error.toString());
            $('#import-modal').modal('hide');
          })
          .importBarangFromJSON(jsonData);

      } catch (error) {
        clearInterval(interval);
        showError('Error reading file: ' + error.toString());
      }
    };

    reader.readAsArrayBuffer(selectedFile);
  }
  </script>

</div>

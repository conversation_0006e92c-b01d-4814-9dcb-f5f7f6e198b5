<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>POS System - Data Barang</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
    }
    .navbar {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .card {
      border: none;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    .table th {
      background-color: #f8f9fa;
      border-top: none;
    }
    .btn-action {
      margin: 0 2px;
    }
    .status-badge {
      font-size: 0.8rem;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
      <a class="navbar-brand" href="?page=dashboard">
        <i class="fas fa-arrow-left me-2"></i>
        Data Barang
      </a>
      
      <div class="navbar-nav ms-auto">
        <span class="navbar-text me-3">
          <i class="fas fa-user me-1"></i>
          <?= user ? user.nama : 'User' ?>
        </span>
        <a class="nav-link" href="#" onclick="logout()">
          <i class="fas fa-sign-out-alt"></i>
        </a>
      </div>
    </div>
  </nav>

  <div class="container-fluid mt-4">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
              <i class="fas fa-boxes me-2"></i>
              Daftar Barang
            </h5>
            <button class="btn btn-primary" onclick="showAddModal()">
              <i class="fas fa-plus me-2"></i>
              Tambah Barang
            </button>
          </div>
          <div class="card-body">
            <div class="row mb-3">
              <div class="col-md-6">
                <input type="text" class="form-control" id="searchInput" placeholder="Cari barang...">
              </div>
            </div>
            
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>Kode</th>
                    <th>Nama Barang</th>
                    <th>Harga</th>
                    <th>Stok</th>
                    <th>Status</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody id="barangTableBody">
                  <tr>
                    <td colspan="6" class="text-center">
                      <i class="fas fa-spinner fa-spin"></i> Memuat data...
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Add/Edit -->
  <div class="modal fade" id="barangModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalTitle">Tambah Barang</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <form id="barangForm">
          <div class="modal-body">
            <input type="hidden" id="barangId">
            
            <div class="mb-3">
              <label class="form-label">Kode Barang *</label>
              <input type="text" class="form-control" id="kodeBarang" required>
            </div>
            
            <div class="mb-3">
              <label class="form-label">Nama Barang *</label>
              <input type="text" class="form-control" id="namaBarang" required>
            </div>
            
            <div class="mb-3">
              <label class="form-label">Harga *</label>
              <input type="number" class="form-control" id="hargaBarang" required min="0">
            </div>
            
            <div class="mb-3">
              <label class="form-label">Stok *</label>
              <input type="number" class="form-control" id="stokBarang" required min="0">
            </div>
            
            <div class="mb-3">
              <label class="form-label">Status</label>
              <select class="form-control" id="statusBarang">
                <option value="aktif">Aktif</option>
                <option value="nonaktif">Non Aktif</option>
              </select>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
            <button type="submit" class="btn btn-primary">Simpan</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    let barangData = [];
    let barangModal;

    document.addEventListener('DOMContentLoaded', function() {
      barangModal = new bootstrap.Modal(document.getElementById('barangModal'));
      loadBarangData();
      setupEventListeners();
    });

    function setupEventListeners() {
      // Search functionality
      document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const filteredData = barangData.filter(item => 
          item.nama.toLowerCase().includes(searchTerm) ||
          item.kode.toLowerCase().includes(searchTerm)
        );
        displayBarangData(filteredData);
      });

      // Form submission
      document.getElementById('barangForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveBarang();
      });
    }

    function loadBarangData() {
      google.script.run
        .withSuccessHandler(function(response) {
          if (response.success) {
            barangData = response.data;
            displayBarangData(barangData);
          } else {
            alert('Error: ' + response.message);
          }
        })
        .withFailureHandler(function(error) {
          alert('Error loading data: ' + error.toString());
        })
        .getBarangList();
    }

    function displayBarangData(data) {
      const tbody = document.getElementById('barangTableBody');
      tbody.innerHTML = '';

      if (data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Tidak ada data</td></tr>';
        return;
      }

      data.forEach(item => {
        const statusClass = item.status === 'aktif' ? 'bg-success' : 'bg-secondary';
        const stockClass = item.stok < 10 ? 'text-danger' : '';
        
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${item.kode}</td>
          <td>${item.nama}</td>
          <td>${formatCurrency(item.harga)}</td>
          <td class="${stockClass}">${item.stok}</td>
          <td><span class="badge ${statusClass} status-badge">${item.status}</span></td>
          <td>
            <button class="btn btn-sm btn-info btn-action" onclick="editBarang(${item.id})" title="Edit">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-danger btn-action" onclick="deleteBarang(${item.id})" title="Hapus">
              <i class="fas fa-trash"></i>
            </button>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    function showAddModal() {
      document.getElementById('modalTitle').textContent = 'Tambah Barang';
      document.getElementById('barangForm').reset();
      document.getElementById('barangId').value = '';
      barangModal.show();
    }

    function editBarang(id) {
      const item = barangData.find(b => b.id === id);
      if (!item) return;

      document.getElementById('modalTitle').textContent = 'Edit Barang';
      document.getElementById('barangId').value = item.id;
      document.getElementById('kodeBarang').value = item.kode;
      document.getElementById('namaBarang').value = item.nama;
      document.getElementById('hargaBarang').value = item.harga;
      document.getElementById('stokBarang').value = item.stok;
      document.getElementById('statusBarang').value = item.status;
      
      barangModal.show();
    }

    function saveBarang() {
      const data = {
        id: document.getElementById('barangId').value || null,
        kode: document.getElementById('kodeBarang').value,
        nama: document.getElementById('namaBarang').value,
        harga: parseInt(document.getElementById('hargaBarang').value),
        stok: parseInt(document.getElementById('stokBarang').value),
        status: document.getElementById('statusBarang').value
      };

      google.script.run
        .withSuccessHandler(function(response) {
          if (response.success) {
            alert(response.message);
            barangModal.hide();
            loadBarangData();
          } else {
            alert('Error: ' + response.message);
          }
        })
        .withFailureHandler(function(error) {
          alert('Error: ' + error.toString());
        })
        .saveBarang(data);
    }

    function deleteBarang(id) {
      const item = barangData.find(b => b.id === id);
      if (!item) return;

      if (confirm(`Apakah Anda yakin ingin menghapus barang "${item.nama}"?`)) {
        google.script.run
          .withSuccessHandler(function(response) {
            if (response.success) {
              alert(response.message);
              loadBarangData();
            } else {
              alert('Error: ' + response.message);
            }
          })
          .withFailureHandler(function(error) {
            alert('Error: ' + error.toString());
          })
          .deleteBarang(id);
      }
    }

    function formatCurrency(amount) {
      return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
    }

    function logout() {
      if (confirm('Apakah Anda yakin ingin logout?')) {
        google.script.run
          .withSuccessHandler(function(response) {
            if (response.success) {
              window.location.href = '?page=login';
            }
          })
          .logout();
      }
    }
  </script>
</body>
</html>

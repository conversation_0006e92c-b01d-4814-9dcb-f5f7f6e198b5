<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>POS System - Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
    }
    .navbar {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .card {
      border: none;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
      transition: transform 0.3s;
    }
    .card:hover {
      transform: translateY(-5px);
    }
    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    .stat-card .card-body {
      padding: 2rem;
    }
    .stat-number {
      font-size: 2.5rem;
      font-weight: bold;
    }
    .menu-card {
      cursor: pointer;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      color: inherit;
    }
    .menu-card:hover {
      color: inherit;
      text-decoration: none;
    }
    .menu-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: #667eea;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
      <a class="navbar-brand" href="#">
        <i class="fas fa-cash-register me-2"></i>
        POS System
      </a>
      
      <div class="navbar-nav ms-auto">
        <div class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
            <i class="fas fa-user me-1"></i>
            <?= user ? user.nama : 'User' ?>
          </a>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
          </ul>
        </div>
      </div>
    </div>
  </nav>

  <div class="container-fluid mt-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
      <div class="col-12">
        <h2>Selamat Datang, <?= user ? user.nama : 'User' ?>!</h2>
        <p class="text-muted">Dashboard Point of Sale System</p>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card stat-card">
          <div class="card-body text-center">
            <i class="fas fa-shopping-cart fa-2x mb-3"></i>
            <div class="stat-number" id="totalTransaksi">0</div>
            <div>Total Transaksi Hari Ini</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card stat-card">
          <div class="card-body text-center">
            <i class="fas fa-money-bill-wave fa-2x mb-3"></i>
            <div class="stat-number" id="totalPendapatan">Rp 0</div>
            <div>Pendapatan Hari Ini</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card stat-card">
          <div class="card-body text-center">
            <i class="fas fa-boxes fa-2x mb-3"></i>
            <div class="stat-number" id="totalBarang">0</div>
            <div>Total Barang</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card stat-card">
          <div class="card-body text-center">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <div class="stat-number" id="stokMenipis">0</div>
            <div>Stok Menipis</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Menu Cards -->
    <div class="row">
      <div class="col-md-3 mb-4">
        <a href="?page=kasir" class="menu-card">
          <div class="card h-100">
            <div class="card-body text-center">
              <i class="fas fa-cash-register menu-icon"></i>
              <h5>Kasir</h5>
              <p class="text-muted">Proses transaksi penjualan</p>
            </div>
          </div>
        </a>
      </div>
      
      <div class="col-md-3 mb-4">
        <a href="?page=barang" class="menu-card">
          <div class="card h-100">
            <div class="card-body text-center">
              <i class="fas fa-boxes menu-icon"></i>
              <h5>Data Barang</h5>
              <p class="text-muted">Kelola data produk</p>
            </div>
          </div>
        </a>
      </div>
      
      <div class="col-md-3 mb-4">
        <a href="?page=laporan" class="menu-card">
          <div class="card h-100">
            <div class="card-body text-center">
              <i class="fas fa-chart-bar menu-icon"></i>
              <h5>Laporan</h5>
              <p class="text-muted">Lihat laporan penjualan</p>
            </div>
          </div>
        </a>
      </div>
      
      <div class="col-md-3 mb-4">
        <div class="card h-100">
          <div class="card-body text-center">
            <i class="fas fa-cog menu-icon"></i>
            <h5>Pengaturan</h5>
            <p class="text-muted">Konfigurasi sistem</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Transactions -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-history me-2"></i>
              Transaksi Terbaru
            </h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>ID Transaksi</th>
                    <th>Tanggal</th>
                    <th>Total</th>
                    <th>Kasir</th>
                  </tr>
                </thead>
                <tbody id="recentTransactions">
                  <tr>
                    <td colspan="4" class="text-center">
                      <i class="fas fa-spinner fa-spin"></i> Memuat data...
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Load dashboard data
    document.addEventListener('DOMContentLoaded', function() {
      loadDashboardData();
    });

    function loadDashboardData() {
      // Load recent transactions
      google.script.run
        .withSuccessHandler(function(response) {
          if (response.success) {
            displayRecentTransactions(response.data);
            updateStatistics(response.data);
          }
        })
        .withFailureHandler(function(error) {
          console.error('Error loading transactions:', error);
        })
        .getTransaksiList();

      // Load barang count
      google.script.run
        .withSuccessHandler(function(response) {
          if (response.success) {
            document.getElementById('totalBarang').textContent = response.data.length;
            
            // Count low stock items
            const lowStock = response.data.filter(item => item.stok < 10).length;
            document.getElementById('stokMenipis').textContent = lowStock;
          }
        })
        .getBarangList();
    }

    function displayRecentTransactions(transactions) {
      const tbody = document.getElementById('recentTransactions');
      tbody.innerHTML = '';

      if (transactions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">Belum ada transaksi</td></tr>';
        return;
      }

      // Show last 5 transactions
      const recent = transactions.slice(0, 5);
      recent.forEach(transaction => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${transaction.id}</td>
          <td>${formatDate(transaction.tanggal)}</td>
          <td>${formatCurrency(transaction.total)}</td>
          <td>${transaction.kasir}</td>
        `;
        tbody.appendChild(row);
      });
    }

    function updateStatistics(transactions) {
      const today = new Date().toDateString();
      const todayTransactions = transactions.filter(t => 
        new Date(t.tanggal).toDateString() === today
      );

      document.getElementById('totalTransaksi').textContent = todayTransactions.length;
      
      const totalPendapatan = todayTransactions.reduce((sum, t) => sum + t.total, 0);
      document.getElementById('totalPendapatan').textContent = formatCurrency(totalPendapatan);
    }

    function formatCurrency(amount) {
      return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
    }

    function formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('id-ID') + ' ' + date.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit'
      });
    }

    function logout() {
      if (confirm('Apakah Anda yakin ingin logout?')) {
        google.script.run
          .withSuccessHandler(function(response) {
            if (response.success) {
              window.location.href = '?page=login';
            }
          })
          .logout();
      }
    }
  </script>
</body>
</html>

<?!= include('header') ?>

<body class="hold-transition login-page">
<div class="login-box">
  <div class="card card-outline card-info">
    <div class="card-header text-center">
      <a href="#" class="h1"><b>DEBUG</b> POS</a>
    </div>
    <div class="card-body">
      <p class="login-box-msg">Setup & Debug POS System</p>

      <div class="alert alert-info">
        <h5><i class="icon fas fa-info"></i> Setup Diperlukan!</h5>
        Sistem POS belum diinisialisasi. Ikuti langkah berikut secara berurutan:
        <ol>
          <li>Create POS Spreadsheet</li>
          <li>Initialize Data</li>
          <li>Test Login</li>
        </ol>
      </div>

      <div class="mb-3">
        <button class="btn btn-warning btn-block" onclick="createSpreadsheet()">
          <i class="fas fa-plus"></i> Create POS Spreadsheet
        </button>
      </div>

      <div class="mb-3">
        <button class="btn btn-info btn-block" onclick="checkUsers()">
          <i class="fas fa-users"></i> Check Users Data
        </button>
      </div>

      <div class="mb-3">
        <button class="btn btn-success btn-block" onclick="initData()">
          <i class="fas fa-cogs"></i> Initialize Data
        </button>
      </div>

      <div class="mb-3">
        <button class="btn btn-warning btn-block" onclick="testLogin()">
          <i class="fas fa-sign-in-alt"></i> Test Login (admin/admin123)
        </button>
      </div>

      <div id="debug-output" class="mt-3" style="display: none;">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Debug Output</h3>
          </div>
          <div class="card-body">
            <pre id="debug-text" style="background: #f4f4f4; padding: 10px; border-radius: 4px; font-size: 12px;"></pre>
          </div>
        </div>
      </div>

      <div class="mt-3">
        <a href="?page=login" class="btn btn-primary btn-block">
          <i class="fas fa-arrow-left"></i> Back to Login
        </a>
      </div>
    </div>
  </div>
</div>

<script>
function showOutput(text) {
  document.getElementById('debug-text').textContent = text;
  document.getElementById('debug-output').style.display = 'block';
}

function createSpreadsheet() {
  google.script.run
    .withSuccessHandler(function(result) {
      showOutput('Create Spreadsheet Result:\n' + JSON.stringify(result, null, 2));
      if (result.success) {
        alert('Spreadsheet berhasil dibuat!\nURL: ' + result.url + '\n\nSekarang coba Initialize Data.');
      }
    })
    .withFailureHandler(function(error) {
      showOutput('Error creating spreadsheet:\n' + error.toString());
    })
    .createPOSSpreadsheet();
}

function checkUsers() {
  google.script.run
    .withSuccessHandler(function(result) {
      showOutput('Users Data:\n' + JSON.stringify(result, null, 2));
    })
    .withFailureHandler(function(error) {
      showOutput('Error checking users:\n' + error.toString());
    })
    .debugGetUsers();
}

function initData() {
  google.script.run
    .withSuccessHandler(function(result) {
      showOutput('Initialize Result:\n' + JSON.stringify(result, null, 2));
    })
    .withFailureHandler(function(error) {
      showOutput('Error initializing:\n' + error.toString());
    })
    .initializeDefaultData();
}

function testLogin() {
  google.script.run
    .withSuccessHandler(function(result) {
      showOutput('Login Test Result:\n' + JSON.stringify(result, null, 2));
      if (result.success) {
        alert('Login test successful! You can now go to login page.');
      }
    })
    .withFailureHandler(function(error) {
      showOutput('Login test error:\n' + error.toString());
    })
    .login('admin', 'admin123');
}
</script>

<?!= include('footer') ?>

<!-- Main Footer -->
<footer class="main-footer">
  <strong>Copyright &copy; 2024 <a href="#">POS System</a>.</strong>
  All rights reserved.
  <div class="float-right d-none d-sm-inline-block">
    <b>Version</b> 1.0.0
  </div>
</footer>

<!-- Control Sidebar -->
<aside class="control-sidebar control-sidebar-dark">
  <!-- Control sidebar content goes here -->
</aside>

<!-- jQuery -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Fix for AdminLTE iframe issues -->
<script>
  // Simple fix for AdminLTE iframe errors
  window.addEventListener('error', function(e) {
    if (e.message && (e.message.includes('autoIframeMode') || e.message.includes('_config is null'))) {
      e.preventDefault();
      return false;
    }
  });

  // Disable AdminLTE iframe mode completely
  window.frameElement = null;
</script>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<!-- Moment.js -->
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<!-- AdminLTE App - Simple Loading -->
<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/js/adminlte.min.js"></script>

<!-- Simple AdminLTE Initialization -->
<script>
$(document).ready(function() {
  // Manual widget initialization to avoid iframe errors
  $('[data-widget="pushmenu"]').click(function(e) {
    e.preventDefault();
    $('body').toggleClass('sidebar-collapse');
  });

  $('[data-widget="navbar-search"]').click(function(e) {
    e.preventDefault();
    $('.navbar-search-block').toggle();
  });

  $('[data-widget="fullscreen"]').click(function(e) {
    e.preventDefault();
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      document.documentElement.requestFullscreen();
    }
  });

  // Card widget collapse
  $('[data-card-widget="collapse"]').click(function(e) {
    e.preventDefault();
    $(this).closest('.card').find('.card-body').slideToggle();
    $(this).find('i').toggleClass('fa-minus fa-plus');
  });
});
</script>
<!-- DataTables -->
<script src="https://cdn.jsdelivr.net/npm/datatables.net@1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net-bs4@1.13.6/js/dataTables.bootstrap4.min.js"></script>
<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>
<!-- Toastr -->
<script src="https://cdn.jsdelivr.net/npm/toastr@2.1.4/toastr.min.js"></script>
<!-- SheetJS for Excel import/export -->
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

<!-- Global JavaScript Functions -->
<script>
// Global configuration
const APP_CONFIG = {
  currency: 'Rp',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: 'HH:mm:ss',
  pagination: 25
};

// Toastr configuration
toastr.options = {
  "closeButton": true,
  "debug": false,
  "newestOnTop": true,
  "progressBar": true,
  "positionClass": "toast-top-right",
  "preventDuplicates": false,
  "onclick": null,
  "showDuration": "300",
  "hideDuration": "1000",
  "timeOut": "5000",
  "extendedTimeOut": "1000",
  "showEasing": "swing",
  "hideEasing": "linear",
  "showMethod": "fadeIn",
  "hideMethod": "fadeOut"
};

// Global utility functions
function formatCurrency(amount) {
  return APP_CONFIG.currency + ' ' + new Intl.NumberFormat('id-ID').format(amount);
}

function formatDate(date) {
  return moment(date).format(APP_CONFIG.dateFormat);
}

function formatDateTime(date) {
  return moment(date).format(APP_CONFIG.dateFormat + ' ' + APP_CONFIG.timeFormat);
}

function showLoading(show = true) {
  if (show) {
    $('.loading').addClass('show');
    $('body').addClass('loading-cursor');
  } else {
    $('.loading').removeClass('show');
    $('body').removeClass('loading-cursor');
  }
}

function showSuccess(message) {
  toastr.success(message);
}

function showError(message) {
  toastr.error(message);
}

function showWarning(message) {
  toastr.warning(message);
}

function showInfo(message) {
  toastr.info(message);
}

// Initialize common components
$(document).ready(function() {
  // Initialize Select2
  $('.select2').select2({
    theme: 'bootstrap'
  });
  
  // Initialize DataTables with simple configuration
  if ($.fn.DataTable) {
    $.extend(true, $.fn.dataTable.defaults, {
      responsive: true,
      lengthChange: true,
      autoWidth: false,
      pageLength: APP_CONFIG.pagination,
      language: {
        "lengthMenu": "Tampilkan _MENU_ data per halaman",
        "zeroRecords": "Data tidak ditemukan",
        "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
        "infoEmpty": "Tidak ada data tersedia",
        "infoFiltered": "(difilter dari _MAX_ total data)",
        "search": "Cari:",
        "paginate": {
          "first": "Pertama",
          "last": "Terakhir",
          "next": "Selanjutnya",
          "previous": "Sebelumnya"
        }
      }
    });
  }
  
  // Initialize currency inputs (simple)
  $('.currency').on('input', function() {
    let value = this.value.replace(/[^\d]/g, '');
    if (value) {
      this.value = 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
    }
  });

  // Initialize number inputs (simple)
  $('.number').on('input', function() {
    this.value = this.value.replace(/[^\d]/g, '');
  });
  
  // Auto-hide alerts
  setTimeout(function() {
    $('.alert').fadeOut();
  }, 5000);
});

// Google Apps Script helper functions
function callServerFunction(functionName, parameters = {}) {
  return new Promise((resolve, reject) => {
    showLoading(true);
    google.script.run
      .withSuccessHandler(function(response) {
        showLoading(false);
        resolve(response);
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
        reject(error);
      })[functionName](parameters);
  });
}

// File upload helper
function handleFileUpload(file, callback) {
  const reader = new FileReader();
  reader.onload = function(e) {
    const data = new Uint8Array(e.target.result);
    const workbook = XLSX.read(data, {type: 'array'});
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    callback(jsonData);
  };
  reader.readAsArrayBuffer(file);
}

// Print helper
function printElement(elementId) {
  const printContents = document.getElementById(elementId).innerHTML;
  const originalContents = document.body.innerHTML;
  document.body.innerHTML = printContents;
  window.print();
  document.body.innerHTML = originalContents;
  location.reload();
}

// Barcode scanner simulation (for testing)
function simulateBarcodeScan(callback) {
  Swal.fire({
    title: 'Scan Barcode',
    input: 'text',
    inputLabel: 'Masukkan kode barcode:',
    inputPlaceholder: 'Contoh: BRG001',
    showCancelButton: true,
    confirmButtonText: 'Scan',
    cancelButtonText: 'Batal'
  }).then((result) => {
    if (result.isConfirmed && result.value) {
      callback(result.value);
    }
  });
}
</script>

</body>
</html>

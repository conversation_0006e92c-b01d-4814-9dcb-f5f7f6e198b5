<!-- Main Footer -->
<footer class="main-footer">
  <strong>Copyright &copy; 2024 <a href="#">POS System</a>.</strong>
  All rights reserved.
  <div class="float-right d-none d-sm-inline-block">
    <b>Version</b> 1.0.0
  </div>
</footer>

<!-- Control Sidebar -->
<aside class="control-sidebar control-sidebar-dark">
  <!-- Control sidebar content goes here -->
</aside>

<!-- jQuery -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<!-- jQuery UI 1.11.4 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js"></script>
<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
<script>
  $.widget.bridge('uibutton', $.ui.button)
</script>
<!-- Bootstrap 4 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.2/js/bootstrap.bundle.min.js"></script>
<!-- ChartJS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<!-- Sparkline -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>
<!-- JQVMap -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/jquery.vmap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/maps/jquery.vmap.usa.js"></script>
<!-- jQuery Knob Chart -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-Knob/1.2.13/jquery.knob.min.js"></script>
<!-- daterangepicker -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.5/daterangepicker.js"></script>
<!-- Tempusdominus Bootstrap 4 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/js/tempusdominus-bootstrap-4.min.js"></script>
<!-- Summernote -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote-bs4.min.js"></script>
<!-- overlayScrollbars -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.1/js/jquery.overlayScrollbars.min.js"></script>
<!-- AdminLTE App -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.js"></script>
<!-- DataTables  & Plugins -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/dataTables.responsive.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/buttons.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/buttons.html5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/buttons.print.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/buttons.colVis.min.js"></script>
<!-- Select2 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.full.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/11.7.12/sweetalert2.all.min.js"></script>
<!-- Toastr -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script>
<!-- InputMask -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8/jquery.inputmask.min.js"></script>
<!-- SheetJS for Excel import/export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<!-- Global JavaScript Functions -->
<script>
// Global configuration
const APP_CONFIG = {
  currency: 'Rp',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: 'HH:mm:ss',
  pagination: 25
};

// Toastr configuration
toastr.options = {
  "closeButton": true,
  "debug": false,
  "newestOnTop": true,
  "progressBar": true,
  "positionClass": "toast-top-right",
  "preventDuplicates": false,
  "onclick": null,
  "showDuration": "300",
  "hideDuration": "1000",
  "timeOut": "5000",
  "extendedTimeOut": "1000",
  "showEasing": "swing",
  "hideEasing": "linear",
  "showMethod": "fadeIn",
  "hideMethod": "fadeOut"
};

// Global utility functions
function formatCurrency(amount) {
  return APP_CONFIG.currency + ' ' + new Intl.NumberFormat('id-ID').format(amount);
}

function formatDate(date) {
  return moment(date).format(APP_CONFIG.dateFormat);
}

function formatDateTime(date) {
  return moment(date).format(APP_CONFIG.dateFormat + ' ' + APP_CONFIG.timeFormat);
}

function showLoading(show = true) {
  if (show) {
    $('.loading').addClass('show');
    $('body').addClass('loading-cursor');
  } else {
    $('.loading').removeClass('show');
    $('body').removeClass('loading-cursor');
  }
}

function showSuccess(message) {
  toastr.success(message);
}

function showError(message) {
  toastr.error(message);
}

function showWarning(message) {
  toastr.warning(message);
}

function showInfo(message) {
  toastr.info(message);
}

// Initialize common components
$(document).ready(function() {
  // Initialize Select2
  $('.select2').select2({
    theme: 'bootstrap'
  });
  
  // Initialize DataTables with default configuration
  if ($.fn.DataTable) {
    $.extend(true, $.fn.dataTable.defaults, {
      responsive: true,
      lengthChange: true,
      autoWidth: false,
      pageLength: APP_CONFIG.pagination,
      language: {
        url: "//cdn.datatables.net/plug-ins/1.10.21/i18n/Indonesian.json"
      },
      dom: 'Bfrtip',
      buttons: [
        'copy', 'csv', 'excel', 'pdf', 'print', 'colvis'
      ]
    });
  }
  
  // Initialize date inputs
  $('.datepicker').daterangepicker({
    singleDatePicker: true,
    showDropdowns: true,
    locale: {
      format: 'DD/MM/YYYY'
    }
  });
  
  // Initialize currency inputs
  $('.currency').inputmask('currency', {
    prefix: 'Rp ',
    groupSeparator: '.',
    radixPoint: ',',
    digits: 0,
    autoGroup: true,
    rightAlign: false
  });
  
  // Initialize number inputs
  $('.number').inputmask('integer', {
    groupSeparator: '.',
    autoGroup: true,
    rightAlign: false
  });
  
  // Auto-hide alerts
  setTimeout(function() {
    $('.alert').fadeOut();
  }, 5000);
});

// Google Apps Script helper functions
function callServerFunction(functionName, parameters = {}) {
  return new Promise((resolve, reject) => {
    showLoading(true);
    google.script.run
      .withSuccessHandler(function(response) {
        showLoading(false);
        resolve(response);
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
        reject(error);
      })[functionName](parameters);
  });
}

// File upload helper
function handleFileUpload(file, callback) {
  const reader = new FileReader();
  reader.onload = function(e) {
    const data = new Uint8Array(e.target.result);
    const workbook = XLSX.read(data, {type: 'array'});
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    callback(jsonData);
  };
  reader.readAsArrayBuffer(file);
}

// Print helper
function printElement(elementId) {
  const printContents = document.getElementById(elementId).innerHTML;
  const originalContents = document.body.innerHTML;
  document.body.innerHTML = printContents;
  window.print();
  document.body.innerHTML = originalContents;
  location.reload();
}

// Barcode scanner simulation (for testing)
function simulateBarcodeScan(callback) {
  Swal.fire({
    title: 'Scan Barcode',
    input: 'text',
    inputLabel: 'Masukkan kode barcode:',
    inputPlaceholder: 'Contoh: BRG001',
    showCancelButton: true,
    confirmButtonText: 'Scan',
    cancelButtonText: 'Batal'
  }).then((result) => {
    if (result.isConfirmed && result.value) {
      callback(result.value);
    }
  });
}
</script>

</body>
</html>

<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>POS System - Point of Sale</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
  <!-- Ionicons -->
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
  <!-- Bootstrap 4 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
  <!-- AdminLTE -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1.0/dist/css/adminlte.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/datatables.net-bs4@1.13.6/css/dataTables.bootstrap4.min.css">
  <!-- Select2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
  <!-- Toastr -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.css">

  <style>
    /* Force sidebar to show */
    .main-sidebar {
      position: fixed !important;
      top: 0;
      left: 0;
      height: 100vh;
      width: 250px;
      z-index: 1040;
      display: block !important;
    }

    .content-wrapper {
      margin-left: 250px !important;
      padding-top: 60px;
    }

    .main-header.navbar {
      position: fixed !important;
      top: 0;
      right: 0;
      left: 250px;
      z-index: 1030;
      display: block !important;
      height: 57px;
      background-color: #fff !important;
      border-bottom: 1px solid #dee2e6;
    }

    /* Sidebar collapsed state */
    .sidebar-collapse .main-sidebar {
      width: 60px;
    }

    .sidebar-collapse .content-wrapper {
      margin-left: 60px !important;
    }

    .sidebar-collapse .main-header.navbar {
      left: 60px;
    }

    .content-wrapper {
      background-color: #f4f6f9;
    }
    
    .card {
      box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
      margin-bottom: 1rem;
    }
    
    .btn {
      border-radius: 0.25rem;
    }
    
    .table th {
      border-top: none;
      font-weight: 600;
      background-color: #f8f9fa;
    }
    
    .sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link.active {
      background-color: #007bff;
      color: #fff;
    }
    
    .navbar-white {
      background-color: #fff !important;
      border-bottom: 1px solid #dee2e6;
    }
    
    .main-footer {
      background-color: #fff;
      border-top: 1px solid #dee2e6;
    }
    
    .pos-item {
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .pos-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,.1);
    }
    
    .cart-item {
      border-bottom: 1px solid #eee;
      padding: 10px 0;
    }
    
    .cart-item:last-child {
      border-bottom: none;
    }
    
    .payment-method {
      cursor: pointer;
      border: 2px solid #dee2e6;
      transition: all 0.3s;
    }
    
    .payment-method.active {
      border-color: #007bff;
      background-color: #e3f2fd;
    }
    
    .payment-method:hover {
      border-color: #007bff;
    }
    
    .barcode-scanner {
      border: 2px dashed #007bff;
      background-color: #f8f9ff;
    }
    
    .loading {
      display: none;
    }
    
    .loading.show {
      display: block;
    }
    
    @media print {
      .no-print {
        display: none !important;
      }
      
      .receipt {
        width: 80mm;
        font-size: 12px;
      }
    }
    
    .select2-container--bootstrap .select2-selection--single {
      height: calc(2.25rem + 2px);
      padding: 0.375rem 0.75rem;
    }
    
    .import-export-buttons {
      margin-bottom: 1rem;
    }
    
    .file-upload-area {
      border: 2px dashed #007bff;
      border-radius: 0.25rem;
      padding: 2rem;
      text-align: center;
      background-color: #f8f9ff;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .file-upload-area:hover {
      background-color: #e3f2fd;
    }
    
    .file-upload-area.dragover {
      border-color: #28a745;
      background-color: #f8fff9;
    }
  </style>
</head>

<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>POS System - Point of Sale</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Ionicons -->
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
  <!-- Tempusdominus Bootstrap 4 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css">
  <!-- iCheck -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css">
  <!-- JQVMap -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/jqvmap.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/css/adminlte.min.css">
  <!-- overlayScrollbars -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.1/css/OverlayScrollbars.min.css">
  <!-- Daterange picker -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.5/daterangepicker.css">
  <!-- summernote -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote-bs4.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/css/buttons.bootstrap4.min.css">
  <!-- Select2 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2-bootstrap-theme/0.1.0-beta.10/select2-bootstrap.min.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/11.7.12/sweetalert2.min.css">
  <!-- Toastr -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css">

  <style>
    /* Fix for AdminLTE iframe issues */
    .iframe-mode .content-wrapper {
      padding-top: 0;
    }

    /* Prevent iframe-related errors */
    .main-header.navbar {
      position: relative !important;
    }

    .content-wrapper {
      background-color: #f4f6f9;
    }
    
    .card {
      box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
      margin-bottom: 1rem;
    }
    
    .btn {
      border-radius: 0.25rem;
    }
    
    .table th {
      border-top: none;
      font-weight: 600;
      background-color: #f8f9fa;
    }
    
    .sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link.active {
      background-color: #007bff;
      color: #fff;
    }
    
    .navbar-white {
      background-color: #fff !important;
      border-bottom: 1px solid #dee2e6;
    }
    
    .main-footer {
      background-color: #fff;
      border-top: 1px solid #dee2e6;
    }
    
    .pos-item {
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .pos-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,.1);
    }
    
    .cart-item {
      border-bottom: 1px solid #eee;
      padding: 10px 0;
    }
    
    .cart-item:last-child {
      border-bottom: none;
    }
    
    .payment-method {
      cursor: pointer;
      border: 2px solid #dee2e6;
      transition: all 0.3s;
    }
    
    .payment-method.active {
      border-color: #007bff;
      background-color: #e3f2fd;
    }
    
    .payment-method:hover {
      border-color: #007bff;
    }
    
    .barcode-scanner {
      border: 2px dashed #007bff;
      background-color: #f8f9ff;
    }
    
    .loading {
      display: none;
    }
    
    .loading.show {
      display: block;
    }
    
    @media print {
      .no-print {
        display: none !important;
      }
      
      .receipt {
        width: 80mm;
        font-size: 12px;
      }
    }
    
    .select2-container--bootstrap .select2-selection--single {
      height: calc(2.25rem + 2px);
      padding: 0.375rem 0.75rem;
    }
    
    .import-export-buttons {
      margin-bottom: 1rem;
    }
    
    .file-upload-area {
      border: 2px dashed #007bff;
      border-radius: 0.25rem;
      padding: 2rem;
      text-align: center;
      background-color: #f8f9ff;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .file-upload-area:hover {
      background-color: #e3f2fd;
    }
    
    .file-upload-area.dragover {
      border-color: #28a745;
      background-color: #f8fff9;
    }
  </style>
</head>

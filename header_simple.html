<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>POS System - Point of Sale</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Bootstrap 4 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.2/css/bootstrap.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/css/dataTables.bootstrap4.min.css">
  <!-- Select2 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/11.7.12/sweetalert2.min.css">
  <!-- Toastr -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css">

  <style>
    :root {
      --primary-color: #007bff;
      --secondary-color: #6c757d;
      --success-color: #28a745;
      --danger-color: #dc3545;
      --warning-color: #ffc107;
      --info-color: #17a2b8;
      --light-color: #f8f9fa;
      --dark-color: #343a40;
    }

    body {
      font-family: 'Source Sans Pro', sans-serif;
      background-color: #f4f6f9;
    }

    /* Sidebar Styles */
    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      width: 250px;
      background: linear-gradient(180deg, #343a40 0%, #495057 100%);
      transition: all 0.3s;
      z-index: 1000;
      overflow-y: auto;
    }

    .sidebar.collapsed {
      width: 60px;
    }

    .sidebar .brand-link {
      display: block;
      padding: 1rem;
      color: white;
      text-decoration: none;
      border-bottom: 1px solid #495057;
    }

    .sidebar .brand-link:hover {
      color: white;
      text-decoration: none;
    }

    .sidebar .nav {
      padding: 1rem 0;
    }

    .sidebar .nav-link {
      color: #c2c7d0;
      padding: 0.75rem 1rem;
      display: block;
      text-decoration: none;
      transition: all 0.3s;
    }

    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
      background-color: var(--primary-color);
      color: white;
      text-decoration: none;
    }

    .sidebar .nav-link i {
      width: 20px;
      margin-right: 10px;
    }

    /* Main Content */
    .main-content {
      margin-left: 250px;
      transition: all 0.3s;
      min-height: 100vh;
    }

    .main-content.expanded {
      margin-left: 60px;
    }

    /* Navbar */
    .top-navbar {
      background: white;
      border-bottom: 1px solid #dee2e6;
      padding: 0.5rem 1rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Cards */
    .card {
      box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
      margin-bottom: 1rem;
      border: none;
    }

    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
    }

    /* Buttons */
    .btn {
      border-radius: 0.25rem;
    }

    /* Tables */
    .table th {
      border-top: none;
      font-weight: 600;
      background-color: #f8f9fa;
    }

    /* POS Specific */
    .pos-item {
      cursor: pointer;
      transition: all 0.3s;
    }

    .pos-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,.1);
    }

    .cart-item {
      border-bottom: 1px solid #eee;
      padding: 10px 0;
    }

    .cart-item:last-child {
      border-bottom: none;
    }

    .payment-method {
      cursor: pointer;
      border: 2px solid #dee2e6;
      transition: all 0.3s;
      border-radius: 0.25rem;
    }

    .payment-method.active {
      border-color: var(--primary-color);
      background-color: #e3f2fd;
    }

    .payment-method:hover {
      border-color: var(--primary-color);
    }

    /* Small boxes for dashboard */
    .small-box {
      border-radius: 0.25rem;
      position: relative;
      display: block;
      margin-bottom: 20px;
      box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    }

    .small-box > .inner {
      padding: 10px;
    }

    .small-box > .small-box-footer {
      position: relative;
      text-align: center;
      padding: 3px 0;
      color: #fff;
      color: rgba(255,255,255,0.8);
      display: block;
      z-index: 10;
      background: rgba(0,0,0,0.1);
      text-decoration: none;
    }

    .small-box > .small-box-footer:hover {
      color: #fff;
      background: rgba(0,0,0,0.15);
      text-decoration: none;
    }

    .small-box h3 {
      font-size: 2.2rem;
      font-weight: bold;
      margin: 0 0 10px 0;
      white-space: nowrap;
      padding: 0;
    }

    .small-box p {
      font-size: 1rem;
    }

    .small-box .icon {
      transition: all 0.3s linear;
      position: absolute;
      top: -10px;
      right: 10px;
      z-index: 0;
      font-size: 90px;
      color: rgba(0,0,0,0.15);
    }

    .bg-info { background-color: var(--info-color) !important; color: white; }
    .bg-success { background-color: var(--success-color) !important; color: white; }
    .bg-warning { background-color: var(--warning-color) !important; color: white; }
    .bg-danger { background-color: var(--danger-color) !important; color: white; }

    /* Responsive */
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
      }
      
      .sidebar.show {
        transform: translateX(0);
      }
      
      .main-content {
        margin-left: 0;
      }
    }

    /* Loading */
    .loading {
      display: none;
    }

    .loading.show {
      display: block;
    }

    /* File upload */
    .file-upload-area {
      border: 2px dashed var(--primary-color);
      border-radius: 0.25rem;
      padding: 2rem;
      text-align: center;
      background-color: #f8f9ff;
      cursor: pointer;
      transition: all 0.3s;
    }

    .file-upload-area:hover {
      background-color: #e3f2fd;
    }

    .file-upload-area.dragover {
      border-color: var(--success-color);
      background-color: #f8fff9;
    }

    /* Print styles */
    @media print {
      .no-print {
        display: none !important;
      }
      
      .receipt {
        width: 80mm;
        font-size: 12px;
      }
    }
  </style>
</head>

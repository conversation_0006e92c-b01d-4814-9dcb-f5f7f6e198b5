<?!= include('header') ?>

<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <!-- Preloader -->
  <div class="preloader flex-column justify-content-center align-items-center">
    <img class="animation__shake" src="https://via.placeholder.com/60x60?text=POS" alt="POS Logo" height="60" width="60">
  </div>

  <?!= include('navbar') ?>
  <?!= include('sidebar') ?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper" style="margin-left: 250px !important; padding-top: 60px;">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Dashboard</h1>
          </div><!-- /.col -->
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="#">Home</a></li>
              <li class="breadcrumb-item active">Dashboard</li>
            </ol>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <!-- Small boxes (Stat box) -->
        <div class="row">
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-info">
              <div class="inner">
                <h3 id="total-penjualan-hari">0</h3>
                <p>Penjualan Hari Ini</p>
              </div>
              <div class="icon">
                <i class="ion ion-bag"></i>
              </div>
              <a href="?page=kasir" class="small-box-footer">Buat Transaksi <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3 id="total-pendapatan-hari">Rp 0</h3>
                <p>Pendapatan Hari Ini</p>
              </div>
              <div class="icon">
                <i class="ion ion-stats-bars"></i>
              </div>
              <a href="?page=laporan" class="small-box-footer">Lihat Laporan <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-warning">
              <div class="inner">
                <h3 id="total-barang">0</h3>
                <p>Total Barang</p>
              </div>
              <div class="icon">
                <i class="ion ion-cube"></i>
              </div>
              <a href="?page=barang" class="small-box-footer">Kelola Barang <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-danger">
              <div class="inner">
                <h3 id="stok-menipis">0</h3>
                <p>Stok Menipis</p>
              </div>
              <div class="icon">
                <i class="ion ion-alert-circled"></i>
              </div>
              <a href="?page=barang&filter=low_stock" class="small-box-footer">Lihat Detail <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
        </div>
        <!-- /.row -->

        <!-- Main row -->
        <div class="row">
          <!-- Left col -->
          <section class="col-lg-7 connectedSortable">
            <!-- Chart -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-chart-pie mr-1"></i>
                  Grafik Penjualan 7 Hari Terakhir
                </h3>
                <div class="card-tools">
                  <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                  </button>
                </div>
              </div>
              <div class="card-body">
                <canvas id="salesChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
              </div>
            </div>
            <!-- /.card -->

            <!-- Top Selling Products -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-trophy mr-1"></i>
                  Produk Terlaris
                </h3>
              </div>
              <div class="card-body p-0">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Produk</th>
                      <th>Terjual</th>
                      <th>Pendapatan</th>
                    </tr>
                  </thead>
                  <tbody id="top-products">
                    <tr>
                      <td colspan="3" class="text-center">Memuat data...</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <!-- /.card -->
          </section>
          <!-- /.Left col -->

          <!-- Right col -->
          <section class="col-lg-5 connectedSortable">
            <!-- Quick Actions -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-bolt mr-1"></i>
                  Aksi Cepat
                </h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-6">
                    <a href="?page=kasir" class="btn btn-primary btn-block">
                      <i class="fas fa-cash-register"></i><br>
                      Kasir
                    </a>
                  </div>
                  <div class="col-6">
                    <a href="?page=barang" class="btn btn-success btn-block">
                      <i class="fas fa-plus"></i><br>
                      Tambah Barang
                    </a>
                  </div>
                </div>
                <div class="row mt-2">
                  <div class="col-6">
                    <a href="?page=laporan" class="btn btn-info btn-block">
                      <i class="fas fa-chart-bar"></i><br>
                      Laporan
                    </a>
                  </div>
                  <div class="col-6">
                    <button class="btn btn-warning btn-block" onclick="scanBarcode()">
                      <i class="fas fa-barcode"></i><br>
                      Scan Barcode
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <!-- /.card -->

            <!-- Recent Transactions -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-history mr-1"></i>
                  Transaksi Terbaru
                </h3>
              </div>
              <div class="card-body p-0">
                <ul class="list-group list-group-flush" id="recent-transactions">
                  <li class="list-group-item text-center">Memuat data...</li>
                </ul>
              </div>
            </div>
            <!-- /.card -->

            <!-- Low Stock Alert -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-exclamation-triangle mr-1"></i>
                  Peringatan Stok
                </h3>
              </div>
              <div class="card-body p-0">
                <ul class="list-group list-group-flush" id="low-stock-items">
                  <li class="list-group-item text-center">Memuat data...</li>
                </ul>
              </div>
            </div>
            <!-- /.card -->
          </section>
          <!-- /.Right col -->
        </div>
        <!-- /.row (main row) -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <?!= include('footer') ?>

  <script>
  let salesChart;

  $(document).ready(function() {
    loadDashboardData();
    initializeSalesChart();
    
    // Refresh data every 5 minutes
    setInterval(loadDashboardData, 300000);
  });

  function loadDashboardData() {
    // Load summary statistics
    google.script.run
      .withSuccessHandler(updateSummaryStats)
      .withFailureHandler(function(error) {
        console.error('Error loading summary stats:', error);
      })
      .getDashboardStats();

    // Load recent transactions
    google.script.run
      .withSuccessHandler(updateRecentTransactions)
      .withFailureHandler(function(error) {
        console.error('Error loading recent transactions:', error);
      })
      .getRecentTransactions();

    // Load low stock items
    google.script.run
      .withSuccessHandler(updateLowStockItems)
      .withFailureHandler(function(error) {
        console.error('Error loading low stock items:', error);
      })
      .getLowStockItems();

    // Load top products
    google.script.run
      .withSuccessHandler(updateTopProducts)
      .withFailureHandler(function(error) {
        console.error('Error loading top products:', error);
      })
      .getTopProducts();

    // Load sales chart data
    google.script.run
      .withSuccessHandler(updateSalesChart)
      .withFailureHandler(function(error) {
        console.error('Error loading sales chart:', error);
      })
      .getSalesChartData();
  }

  function updateSummaryStats(data) {
    $('#total-penjualan-hari').text(data.totalSalesToday || 0);
    $('#total-pendapatan-hari').text(formatCurrency(data.totalRevenueToday || 0));
    $('#total-barang').text(data.totalProducts || 0);
    $('#stok-menipis').text(data.lowStockCount || 0);
  }

  function updateRecentTransactions(data) {
    const container = $('#recent-transactions');
    container.empty();
    
    if (data && data.length > 0) {
      data.slice(0, 5).forEach(transaction => {
        container.append(`
          <li class="list-group-item d-flex justify-content-between align-items-center">
            <div>
              <strong>#${transaction.nomor_transaksi}</strong><br>
              <small class="text-muted">${formatDateTime(transaction.created_at)}</small>
            </div>
            <span class="badge badge-primary badge-pill">${formatCurrency(transaction.total_bayar)}</span>
          </li>
        `);
      });
    } else {
      container.append('<li class="list-group-item text-center text-muted">Belum ada transaksi</li>');
    }
  }

  function updateLowStockItems(data) {
    const container = $('#low-stock-items');
    container.empty();
    
    if (data && data.length > 0) {
      data.slice(0, 5).forEach(item => {
        container.append(`
          <li class="list-group-item d-flex justify-content-between align-items-center">
            <div>
              <strong>${item.nama_barang}</strong><br>
              <small class="text-muted">${item.kode_barang}</small>
            </div>
            <span class="badge badge-danger badge-pill">${item.stok_saat_ini}</span>
          </li>
        `);
      });
    } else {
      container.append('<li class="list-group-item text-center text-muted">Semua stok aman</li>');
    }
  }

  function updateTopProducts(data) {
    const container = $('#top-products');
    container.empty();
    
    if (data && data.length > 0) {
      data.slice(0, 5).forEach(product => {
        container.append(`
          <tr>
            <td>${product.nama_barang}</td>
            <td><span class="badge badge-success">${product.total_terjual}</span></td>
            <td>${formatCurrency(product.total_pendapatan)}</td>
          </tr>
        `);
      });
    } else {
      container.append('<tr><td colspan="3" class="text-center text-muted">Belum ada data</td></tr>');
    }
  }

  function initializeSalesChart() {
    const ctx = document.getElementById('salesChart').getContext('2d');
    salesChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: [{
          label: 'Penjualan',
          data: [],
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  function updateSalesChart(data) {
    if (salesChart && data) {
      salesChart.data.labels = data.labels || [];
      salesChart.data.datasets[0].data = data.values || [];
      salesChart.update();
    }
  }

  function scanBarcode() {
    simulateBarcodeScan(function(barcode) {
      // Search for product by barcode
      google.script.run
        .withSuccessHandler(function(result) {
          if (result.success && result.data.length > 0) {
            const product = result.data[0];
            Swal.fire({
              title: 'Produk Ditemukan',
              html: `
                <div class="text-left">
                  <p><strong>Kode:</strong> ${product.kode_barang}</p>
                  <p><strong>Nama:</strong> ${product.nama_barang}</p>
                  <p><strong>Harga:</strong> ${formatCurrency(product.harga_jual)}</p>
                  <p><strong>Stok:</strong> ${product.stok_saat_ini}</p>
                </div>
              `,
              icon: 'success',
              showCancelButton: true,
              confirmButtonText: 'Buka Kasir',
              cancelButtonText: 'Tutup'
            }).then((result) => {
              if (result.isConfirmed) {
                window.location.href = '?page=kasir&barcode=' + encodeURIComponent(barcode);
              }
            });
          } else {
            Swal.fire('Produk Tidak Ditemukan', 'Barcode "' + barcode + '" tidak ditemukan dalam database.', 'warning');
          }
        })
        .withFailureHandler(function(error) {
          Swal.fire('Error', 'Gagal mencari produk: ' + error.toString(), 'error');
        })
        .searchBarang(barcode);
    });
  }
  </script>

</div>
<!-- ./wrapper -->

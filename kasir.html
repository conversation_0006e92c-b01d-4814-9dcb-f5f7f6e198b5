<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>POS System - Kasir</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
    }
    .navbar {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .product-card {
      cursor: pointer;
      transition: transform 0.2s;
      border: none;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .product-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }
    .cart-section {
      background: white;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      padding: 1.5rem;
      height: calc(100vh - 120px);
      overflow-y: auto;
    }
    .cart-item {
      border-bottom: 1px solid #eee;
      padding: 10px 0;
    }
    .cart-item:last-child {
      border-bottom: none;
    }
    .total-section {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 10px;
      padding: 1rem;
      margin-top: 1rem;
    }
    .btn-checkout {
      background: #28a745;
      border: none;
      border-radius: 10px;
      padding: 15px;
      font-size: 1.1rem;
      font-weight: bold;
    }
    .btn-checkout:hover {
      background: #218838;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
      <a class="navbar-brand" href="?page=dashboard">
        <i class="fas fa-arrow-left me-2"></i>
        Kasir
      </a>
      
      <div class="navbar-nav ms-auto">
        <span class="navbar-text me-3">
          <i class="fas fa-user me-1"></i>
          <?= user ? user.nama : 'User' ?>
        </span>
        <a class="nav-link" href="#" onclick="logout()">
          <i class="fas fa-sign-out-alt"></i>
        </a>
      </div>
    </div>
  </nav>

  <div class="container-fluid mt-3">
    <div class="row">
      <!-- Products Section -->
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-boxes me-2"></i>
              Daftar Produk
            </h5>
            <div class="mt-2">
              <input type="text" class="form-control" id="searchProduct" placeholder="Cari produk...">
            </div>
          </div>
          <div class="card-body">
            <div class="row" id="productList">
              <div class="col-12 text-center">
                <i class="fas fa-spinner fa-spin"></i> Memuat produk...
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cart Section -->
      <div class="col-md-4">
        <div class="cart-section">
          <h5 class="mb-3">
            <i class="fas fa-shopping-cart me-2"></i>
            Keranjang Belanja
          </h5>
          
          <div id="cartItems">
            <div class="text-center text-muted">
              <i class="fas fa-shopping-cart fa-3x mb-3"></i>
              <p>Keranjang kosong</p>
            </div>
          </div>

          <div class="total-section">
            <div class="d-flex justify-content-between mb-2">
              <span>Subtotal:</span>
              <span id="subtotal">Rp 0</span>
            </div>
            <div class="d-flex justify-content-between mb-2">
              <span>Pajak (10%):</span>
              <span id="tax">Rp 0</span>
            </div>
            <hr>
            <div class="d-flex justify-content-between mb-3">
              <strong>Total:</strong>
              <strong id="total">Rp 0</strong>
            </div>
            
            <div class="mb-3">
              <label class="form-label">Jumlah Bayar:</label>
              <input type="number" class="form-control" id="paymentAmount" placeholder="0">
            </div>
            
            <div class="mb-3">
              <label class="form-label">Kembalian:</label>
              <div class="form-control" id="change">Rp 0</div>
            </div>
            
            <button class="btn btn-success btn-checkout w-100" onclick="processCheckout()" disabled>
              <i class="fas fa-credit-card me-2"></i>
              Proses Pembayaran
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    let products = [];
    let cart = [];

    document.addEventListener('DOMContentLoaded', function() {
      loadProducts();
      setupEventListeners();
    });

    function setupEventListeners() {
      // Search functionality
      document.getElementById('searchProduct').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const filteredProducts = products.filter(product => 
          product.nama.toLowerCase().includes(searchTerm) ||
          product.kode.toLowerCase().includes(searchTerm)
        );
        displayProducts(filteredProducts);
      });

      // Payment amount change
      document.getElementById('paymentAmount').addEventListener('input', function() {
        calculateChange();
      });
    }

    function loadProducts() {
      google.script.run
        .withSuccessHandler(function(response) {
          if (response.success) {
            products = response.data.filter(p => p.status === 'aktif' && p.stok > 0);
            displayProducts(products);
          }
        })
        .withFailureHandler(function(error) {
          console.error('Error loading products:', error);
        })
        .getBarangList();
    }

    function displayProducts(productList) {
      const container = document.getElementById('productList');
      container.innerHTML = '';

      if (productList.length === 0) {
        container.innerHTML = '<div class="col-12 text-center text-muted">Tidak ada produk tersedia</div>';
        return;
      }

      productList.forEach(product => {
        const col = document.createElement('div');
        col.className = 'col-md-6 col-lg-4 mb-3';
        col.innerHTML = `
          <div class="card product-card" onclick="addToCart(${product.id})">
            <div class="card-body">
              <h6 class="card-title">${product.nama}</h6>
              <p class="card-text">
                <small class="text-muted">${product.kode}</small><br>
                <strong>${formatCurrency(product.harga)}</strong><br>
                <small>Stok: ${product.stok}</small>
              </p>
            </div>
          </div>
        `;
        container.appendChild(col);
      });
    }

    function addToCart(productId) {
      const product = products.find(p => p.id === productId);
      if (!product) return;

      const existingItem = cart.find(item => item.id === productId);
      if (existingItem) {
        if (existingItem.qty < product.stok) {
          existingItem.qty++;
        } else {
          alert('Stok tidak mencukupi');
          return;
        }
      } else {
        cart.push({
          id: product.id,
          nama: product.nama,
          harga: product.harga,
          qty: 1,
          stok: product.stok
        });
      }

      updateCartDisplay();
    }

    function removeFromCart(productId) {
      cart = cart.filter(item => item.id !== productId);
      updateCartDisplay();
    }

    function updateQuantity(productId, newQty) {
      const item = cart.find(item => item.id === productId);
      if (item) {
        if (newQty <= 0) {
          removeFromCart(productId);
        } else if (newQty <= item.stok) {
          item.qty = newQty;
          updateCartDisplay();
        } else {
          alert('Stok tidak mencukupi');
        }
      }
    }

    function updateCartDisplay() {
      const container = document.getElementById('cartItems');
      
      if (cart.length === 0) {
        container.innerHTML = `
          <div class="text-center text-muted">
            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
            <p>Keranjang kosong</p>
          </div>
        `;
      } else {
        container.innerHTML = cart.map(item => `
          <div class="cart-item">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="mb-1">${item.nama}</h6>
                <small class="text-muted">${formatCurrency(item.harga)} x ${item.qty}</small>
              </div>
              <div class="d-flex align-items-center">
                <button class="btn btn-sm btn-outline-secondary me-1" onclick="updateQuantity(${item.id}, ${item.qty - 1})">-</button>
                <span class="mx-2">${item.qty}</span>
                <button class="btn btn-sm btn-outline-secondary me-2" onclick="updateQuantity(${item.id}, ${item.qty + 1})">+</button>
                <button class="btn btn-sm btn-danger" onclick="removeFromCart(${item.id})">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
            <div class="text-end mt-1">
              <strong>${formatCurrency(item.harga * item.qty)}</strong>
            </div>
          </div>
        `).join('');
      }

      updateTotals();
    }

    function updateTotals() {
      const subtotal = cart.reduce((sum, item) => sum + (item.harga * item.qty), 0);
      const tax = subtotal * 0.1;
      const total = subtotal + tax;

      document.getElementById('subtotal').textContent = formatCurrency(subtotal);
      document.getElementById('tax').textContent = formatCurrency(tax);
      document.getElementById('total').textContent = formatCurrency(total);

      // Enable/disable checkout button
      const checkoutBtn = document.querySelector('.btn-checkout');
      checkoutBtn.disabled = cart.length === 0;

      calculateChange();
    }

    function calculateChange() {
      const total = cart.reduce((sum, item) => sum + (item.harga * item.qty), 0) * 1.1;
      const payment = parseFloat(document.getElementById('paymentAmount').value) || 0;
      const change = payment - total;

      document.getElementById('change').textContent = formatCurrency(Math.max(0, change));
      
      // Enable checkout only if payment is sufficient
      const checkoutBtn = document.querySelector('.btn-checkout');
      checkoutBtn.disabled = cart.length === 0 || payment < total;
    }

    function processCheckout() {
      if (cart.length === 0) return;

      const subtotal = cart.reduce((sum, item) => sum + (item.harga * item.qty), 0);
      const total = subtotal * 1.1;
      const payment = parseFloat(document.getElementById('paymentAmount').value);

      if (payment < total) {
        alert('Jumlah pembayaran kurang');
        return;
      }

      // Process transaction
      google.script.run
        .withSuccessHandler(function(response) {
          if (response.success) {
            alert(`Transaksi berhasil!\nID: ${response.transaksi.id}\nKembalian: ${formatCurrency(response.transaksi.kembalian)}`);
            
            // Reset cart
            cart = [];
            updateCartDisplay();
            document.getElementById('paymentAmount').value = '';
            
            // Reload products to update stock
            loadProducts();
          } else {
            alert('Transaksi gagal: ' + response.message);
          }
        })
        .withFailureHandler(function(error) {
          alert('Error: ' + error.toString());
        })
        .processTransaction(cart, total, payment);
    }

    function formatCurrency(amount) {
      return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
    }

    function logout() {
      if (confirm('Apakah Anda yakin ingin logout?')) {
        google.script.run
          .withSuccessHandler(function(response) {
            if (response.success) {
              window.location.href = '?page=login';
            }
          })
          .logout();
      }
    }
  </script>
</body>
</html>

<?!= include('header') ?>

<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <?!= include('navbar') ?>
  <?!= include('sidebar') ?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Kasir</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="?page=index">Home</a></li>
              <li class="breadcrumb-item active">Kasir</li>
            </ol>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <!-- Left Column - Product Selection -->
          <div class="col-lg-8">
            <!-- Barcode Scanner -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-barcode mr-1"></i>
                  Scanner Barcode
                </h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-8">
                    <div class="input-group">
                      <input type="text" class="form-control" id="barcode-input" placeholder="Scan atau ketik kode barang..." autofocus>
                      <div class="input-group-append">
                        <button class="btn btn-primary" type="button" onclick="searchProduct()">
                          <i class="fas fa-search"></i> Cari
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <button class="btn btn-success btn-block" onclick="simulateBarcodeScan(addProductToCart)">
                      <i class="fas fa-barcode"></i> Scan Barcode
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Product Grid -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-th mr-1"></i>
                  Pilih Produk
                </h3>
                <div class="card-tools">
                  <select class="form-control form-control-sm" id="category-filter" style="width: 200px;">
                    <option value="">Semua Kategori</option>
                  </select>
                </div>
              </div>
              <div class="card-body">
                <div class="row" id="product-grid">
                  <div class="col-12 text-center">
                    <i class="fas fa-spinner fa-spin"></i> Memuat produk...
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column - Shopping Cart -->
          <div class="col-lg-4">
            <!-- Cart -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-shopping-cart mr-1"></i>
                  Keranjang Belanja
                </h3>
                <div class="card-tools">
                  <button class="btn btn-tool" onclick="clearCart()">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
              <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                <div id="cart-items">
                  <div class="p-3 text-center text-muted">
                    Keranjang kosong
                  </div>
                </div>
              </div>
            </div>

            <!-- Customer Selection -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-user mr-1"></i>
                  Pelanggan
                </h3>
              </div>
              <div class="card-body">
                <select class="form-control select2" id="customer-select">
                  <option value="">Pelanggan Umum</option>
                </select>
              </div>
            </div>

            <!-- Payment Summary -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-calculator mr-1"></i>
                  Ringkasan Pembayaran
                </h3>
              </div>
              <div class="card-body">
                <table class="table table-sm">
                  <tr>
                    <td>Subtotal:</td>
                    <td class="text-right" id="subtotal">Rp 0</td>
                  </tr>
                  <tr>
                    <td>Diskon:</td>
                    <td class="text-right">
                      <div class="input-group input-group-sm">
                        <input type="number" class="form-control text-right" id="discount-percent" placeholder="0" min="0" max="100">
                        <div class="input-group-append">
                          <span class="input-group-text">%</span>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>Pajak (10%):</td>
                    <td class="text-right" id="tax">Rp 0</td>
                  </tr>
                  <tr class="font-weight-bold">
                    <td>Total:</td>
                    <td class="text-right" id="total">Rp 0</td>
                  </tr>
                </table>
              </div>
            </div>

            <!-- Payment Methods -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-credit-card mr-1"></i>
                  Metode Pembayaran
                </h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-4">
                    <div class="payment-method text-center p-2" data-method="cash">
                      <i class="fas fa-money-bill-wave fa-2x"></i>
                      <br><small>Cash</small>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="payment-method text-center p-2" data-method="card">
                      <i class="fas fa-credit-card fa-2x"></i>
                      <br><small>Card</small>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="payment-method text-center p-2" data-method="digital">
                      <i class="fas fa-mobile-alt fa-2x"></i>
                      <br><small>Digital</small>
                    </div>
                  </div>
                </div>
                
                <!-- Payment Amount Inputs -->
                <div class="mt-3" id="payment-inputs" style="display: none;">
                  <div class="payment-input" data-method="cash" style="display: none;">
                    <label>Jumlah Cash:</label>
                    <input type="text" class="form-control currency" id="cash-amount" placeholder="0">
                  </div>
                  <div class="payment-input" data-method="card" style="display: none;">
                    <label>Jumlah Card:</label>
                    <input type="text" class="form-control currency" id="card-amount" placeholder="0">
                  </div>
                  <div class="payment-input" data-method="digital" style="display: none;">
                    <label>Jumlah Digital:</label>
                    <input type="text" class="form-control currency" id="digital-amount" placeholder="0">
                  </div>
                  
                  <div class="mt-2">
                    <strong>Kembalian: <span id="change-amount">Rp 0</span></strong>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="card">
              <div class="card-body">
                <button class="btn btn-success btn-block btn-lg" onclick="processPayment()" id="pay-button" disabled>
                  <i class="fas fa-cash-register"></i> Bayar
                </button>
                <button class="btn btn-secondary btn-block" onclick="holdTransaction()">
                  <i class="fas fa-pause"></i> Tahan Transaksi
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <?!= include('footer') ?>

  <script>
  let cart = [];
  let products = [];
  let customers = [];
  let categories = [];
  let selectedPaymentMethods = [];

  $(document).ready(function() {
    loadInitialData();
    setupEventHandlers();
  });

  function loadInitialData() {
    // Load products
    google.script.run
      .withSuccessHandler(function(result) {
        if (result.success) {
          products = result.data;
          displayProducts(products);
        }
      })
      .getBarangList();

    // Load customers
    google.script.run
      .withSuccessHandler(function(result) {
        if (result.success) {
          customers = result.data;
          populateCustomerSelect();
        }
      })
      .getPelangganList();

    // Load categories
    google.script.run
      .withSuccessHandler(function(result) {
        if (result.success) {
          categories = result.data;
          populateCategoryFilter();
        }
      })
      .getKategoriList();
  }

  function setupEventHandlers() {
    // Barcode input
    $('#barcode-input').on('keypress', function(e) {
      if (e.which === 13) {
        searchProduct();
      }
    });

    // Discount calculation
    $('#discount-percent').on('input', calculateTotal);

    // Payment method selection
    $('.payment-method').on('click', function() {
      const method = $(this).data('method');
      togglePaymentMethod(method);
    });

    // Payment amount inputs
    $('.currency').on('input', calculateChange);
  }

  function displayProducts(productList) {
    const grid = $('#product-grid');
    grid.empty();

    if (productList.length === 0) {
      grid.append('<div class="col-12 text-center text-muted">Tidak ada produk ditemukan</div>');
      return;
    }

    productList.forEach(product => {
      if (product.status === 'aktif') {
        grid.append(`
          <div class="col-md-4 col-sm-6 mb-3">
            <div class="card pos-item" onclick="addProductToCart('${product.kode_barang}')">
              <div class="card-body text-center">
                <img src="${product.gambar_url || 'https://via.placeholder.com/100x100?text=' + product.nama_barang.charAt(0)}" 
                     class="img-fluid mb-2" style="max-height: 80px;">
                <h6 class="card-title">${product.nama_barang}</h6>
                <p class="card-text">
                  <small class="text-muted">${product.kode_barang}</small><br>
                  <strong>${formatCurrency(product.harga_jual)}</strong><br>
                  <small>Stok: ${product.stok_saat_ini}</small>
                </p>
              </div>
            </div>
          </div>
        `);
      }
    });
  }

  function populateCustomerSelect() {
    const select = $('#customer-select');
    select.empty().append('<option value="">Pelanggan Umum</option>');
    
    customers.forEach(customer => {
      if (customer.status === 'aktif') {
        select.append(`<option value="${customer.id}">${customer.nama_pelanggan} - ${customer.telepon}</option>`);
      }
    });
  }

  function populateCategoryFilter() {
    const select = $('#category-filter');
    select.empty().append('<option value="">Semua Kategori</option>');
    
    categories.forEach(category => {
      if (category.status === 'aktif') {
        select.append(`<option value="${category.id}">${category.nama_kategori}</option>`);
      }
    });

    select.on('change', function() {
      const categoryId = $(this).val();
      if (categoryId) {
        const filtered = products.filter(p => p.kategori_id == categoryId);
        displayProducts(filtered);
      } else {
        displayProducts(products);
      }
    });
  }

  function searchProduct() {
    const keyword = $('#barcode-input').val().trim();
    if (!keyword) return;

    const found = products.find(p => 
      p.kode_barang.toLowerCase() === keyword.toLowerCase() ||
      p.nama_barang.toLowerCase().includes(keyword.toLowerCase())
    );

    if (found) {
      addProductToCart(found.kode_barang);
      $('#barcode-input').val('').focus();
    } else {
      showError('Produk tidak ditemukan');
      $('#barcode-input').select();
    }
  }

  function addProductToCart(kodeBarang) {
    const product = products.find(p => p.kode_barang === kodeBarang);
    if (!product) {
      showError('Produk tidak ditemukan');
      return;
    }

    if (product.stok_saat_ini <= 0) {
      showError('Stok produk habis');
      return;
    }

    const existingItem = cart.find(item => item.kode_barang === kodeBarang);
    if (existingItem) {
      if (existingItem.quantity >= product.stok_saat_ini) {
        showError('Stok tidak mencukupi');
        return;
      }
      existingItem.quantity++;
    } else {
      cart.push({
        kode_barang: product.kode_barang,
        nama_barang: product.nama_barang,
        harga_jual: product.harga_jual,
        quantity: 1,
        stok_tersedia: product.stok_saat_ini
      });
    }

    updateCartDisplay();
    calculateTotal();
    showSuccess('Produk ditambahkan ke keranjang');
  }

  function updateCartDisplay() {
    const container = $('#cart-items');
    container.empty();

    if (cart.length === 0) {
      container.append('<div class="p-3 text-center text-muted">Keranjang kosong</div>');
      $('#pay-button').prop('disabled', true);
      return;
    }

    cart.forEach((item, index) => {
      container.append(`
        <div class="cart-item p-2">
          <div class="d-flex justify-content-between align-items-center">
            <div class="flex-grow-1">
              <strong>${item.nama_barang}</strong><br>
              <small class="text-muted">${item.kode_barang}</small><br>
              <small>${formatCurrency(item.harga_jual)} x ${item.quantity}</small>
            </div>
            <div class="text-right">
              <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-secondary" onclick="updateQuantity(${index}, -1)">-</button>
                <button class="btn btn-outline-secondary" onclick="updateQuantity(${index}, 1)">+</button>
                <button class="btn btn-outline-danger" onclick="removeFromCart(${index})">×</button>
              </div>
              <br>
              <strong>${formatCurrency(item.harga_jual * item.quantity)}</strong>
            </div>
          </div>
        </div>
      `);
    });

    $('#pay-button').prop('disabled', false);
  }

  function updateQuantity(index, change) {
    const item = cart[index];
    const newQuantity = item.quantity + change;

    if (newQuantity <= 0) {
      removeFromCart(index);
      return;
    }

    if (newQuantity > item.stok_tersedia) {
      showError('Stok tidak mencukupi');
      return;
    }

    item.quantity = newQuantity;
    updateCartDisplay();
    calculateTotal();
  }

  function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
    calculateTotal();
  }

  function clearCart() {
    if (cart.length === 0) return;

    Swal.fire({
      title: 'Kosongkan Keranjang?',
      text: 'Semua item akan dihapus dari keranjang',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Ya, Kosongkan',
      cancelButtonText: 'Batal'
    }).then((result) => {
      if (result.isConfirmed) {
        cart = [];
        updateCartDisplay();
        calculateTotal();
        showSuccess('Keranjang dikosongkan');
      }
    });
  }

  function calculateTotal() {
    const subtotal = cart.reduce((sum, item) => sum + (item.harga_jual * item.quantity), 0);
    const discountPercent = parseFloat($('#discount-percent').val()) || 0;
    const discountAmount = subtotal * (discountPercent / 100);
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = afterDiscount * 0.1; // 10% tax
    const total = afterDiscount + taxAmount;

    $('#subtotal').text(formatCurrency(subtotal));
    $('#tax').text(formatCurrency(taxAmount));
    $('#total').text(formatCurrency(total));

    calculateChange();
  }

  function togglePaymentMethod(method) {
    const element = $(`.payment-method[data-method="${method}"]`);
    const isSelected = element.hasClass('active');

    if (isSelected) {
      element.removeClass('active');
      selectedPaymentMethods = selectedPaymentMethods.filter(m => m !== method);
      $(`.payment-input[data-method="${method}"]`).hide();
    } else {
      element.addClass('active');
      selectedPaymentMethods.push(method);
      $(`.payment-input[data-method="${method}"]`).show();
    }

    $('#payment-inputs').toggle(selectedPaymentMethods.length > 0);
    calculateChange();
  }

  function calculateChange() {
    const total = parseFloat($('#total').text().replace(/[^\d]/g, '')) || 0;
    let totalPaid = 0;

    selectedPaymentMethods.forEach(method => {
      const amount = parseFloat($(`#${method}-amount`).val().replace(/[^\d]/g, '')) || 0;
      totalPaid += amount;
    });

    const change = totalPaid - total;
    $('#change-amount').text(formatCurrency(Math.max(0, change)));
  }

  function processPayment() {
    if (cart.length === 0) {
      showError('Keranjang kosong');
      return;
    }

    if (selectedPaymentMethods.length === 0) {
      showError('Pilih metode pembayaran');
      return;
    }

    const total = parseFloat($('#total').text().replace(/[^\d]/g, ''));
    let totalPaid = 0;

    const paymentData = {};
    selectedPaymentMethods.forEach(method => {
      const amount = parseFloat($(`#${method}-amount`).val().replace(/[^\d]/g, '')) || 0;
      paymentData[method + '_amount'] = amount;
      totalPaid += amount;
    });

    if (totalPaid < total) {
      showError('Jumlah pembayaran kurang');
      return;
    }

    // Prepare transaction data
    const transactionData = {
      items: cart,
      customer_id: $('#customer-select').val() || null,
      discount_percent: parseFloat($('#discount-percent').val()) || 0,
      payment_methods: selectedPaymentMethods,
      payment_amounts: paymentData,
      total_paid: totalPaid
    };

    // Process transaction
    showLoading(true);
    google.script.run
      .withSuccessHandler(function(result) {
        showLoading(false);
        if (result.success) {
          showSuccess('Transaksi berhasil');
          printReceipt(result.transaction);
          resetTransaction();
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
      })
      .processTransaction(transactionData);
  }

  function printReceipt(transaction) {
    // Implementation for receipt printing
    Swal.fire({
      title: 'Transaksi Berhasil',
      html: `
        <div class="receipt">
          <h4>STRUK PEMBAYARAN</h4>
          <p>No. Transaksi: ${transaction.nomor_transaksi}</p>
          <p>Tanggal: ${formatDateTime(transaction.created_at)}</p>
          <hr>
          <p>Total: ${formatCurrency(transaction.total_bayar)}</p>
          <p>Kembalian: ${formatCurrency(transaction.kembalian)}</p>
        </div>
      `,
      icon: 'success',
      showCancelButton: true,
      confirmButtonText: 'Print',
      cancelButtonText: 'Tutup'
    }).then((result) => {
      if (result.isConfirmed) {
        // Print receipt
        window.print();
      }
    });
  }

  function resetTransaction() {
    cart = [];
    selectedPaymentMethods = [];
    $('#barcode-input').val('');
    $('#customer-select').val('').trigger('change');
    $('#discount-percent').val('');
    $('.payment-method').removeClass('active');
    $('.payment-input').hide();
    $('#payment-inputs').hide();
    $('.currency').val('');
    updateCartDisplay();
    calculateTotal();
    $('#barcode-input').focus();
  }

  function holdTransaction() {
    if (cart.length === 0) {
      showError('Keranjang kosong');
      return;
    }

    Swal.fire({
      title: 'Tahan Transaksi',
      text: 'Transaksi akan disimpan sementara',
      icon: 'info',
      showCancelButton: true,
      confirmButtonText: 'Ya, Tahan',
      cancelButtonText: 'Batal'
    }).then((result) => {
      if (result.isConfirmed) {
        // Save transaction to temporary storage
        const heldTransaction = {
          cart: [...cart],
          customer_id: $('#customer-select').val(),
          discount_percent: $('#discount-percent').val(),
          timestamp: new Date()
        };
        
        localStorage.setItem('held_transaction_' + Date.now(), JSON.stringify(heldTransaction));
        resetTransaction();
        showSuccess('Transaksi ditahan');
      }
    });
  }
  </script>

</div>

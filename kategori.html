<?!= include('header') ?>

<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <?!= include('navbar') ?>
  <?!= include('sidebar') ?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Data Kategori</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="?page=index">Home</a></li>
              <li class="breadcrumb-item"><a href="#">Master Data</a></li>
              <li class="breadcrumb-item active">Kategori</li>
            </ol>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-tags mr-1"></i>
                  Daftar Kategori
                </h3>
                <div class="card-tools">
                  <button class="btn btn-primary btn-sm" onclick="showAddModal()">
                    <i class="fas fa-plus"></i> Tambah Kategori
                  </button>
                </div>
              </div>
              <div class="card-body">
                <table class="table table-bordered table-striped" id="kategori-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Nama Kategori</th>
                      <th>Deskripsi</th>
                      <th>Status</th>
                      <th>Tanggal Dibuat</th>
                      <th>Aksi</th>
                    </tr>
                  </thead>
                  <tbody id="kategori-tbody">
                    <tr>
                      <td colspan="6" class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Memuat data...
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- Add/Edit Modal -->
  <div class="modal fade" id="kategori-modal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title" id="modal-title">Tambah Kategori</h4>
          <button type="button" class="close" data-dismiss="modal">
            <span>&times;</span>
          </button>
        </div>
        <form id="kategori-form">
          <div class="modal-body">
            <input type="hidden" id="kategori-id">
            <div class="form-group">
              <label for="nama-kategori">Nama Kategori *</label>
              <input type="text" class="form-control" id="nama-kategori" required>
            </div>
            <div class="form-group">
              <label for="deskripsi">Deskripsi</label>
              <textarea class="form-control" id="deskripsi" rows="3"></textarea>
            </div>
            <div class="form-group">
              <label for="status">Status</label>
              <select class="form-control" id="status">
                <option value="aktif">Aktif</option>
                <option value="nonaktif">Non Aktif</option>
              </select>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
            <button type="submit" class="btn btn-primary">Simpan</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <?!= include('footer') ?>

  <script>
  let kategoriData = [];

  $(document).ready(function() {
    loadKategoriData();
    setupEventHandlers();
  });

  function setupEventHandlers() {
    // Form submission
    $('#kategori-form').on('submit', function(e) {
      e.preventDefault();
      saveKategori();
    });

    // Initialize DataTable
    $('#kategori-table').DataTable({
      responsive: true,
      lengthChange: true,
      autoWidth: false,
      buttons: ['copy', 'csv', 'excel', 'pdf', 'print', 'colvis']
    }).buttons().container().appendTo('#kategori-table_wrapper .col-md-6:eq(0)');
  }

  function loadKategoriData() {
    google.script.run
      .withSuccessHandler(function(result) {
        if (result.success) {
          kategoriData = result.data;
          displayKategoriData(kategoriData);
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showError('Error loading data: ' + error.toString());
      })
      .getKategoriList();
  }

  function displayKategoriData(data) {
    const tbody = $('#kategori-tbody');
    tbody.empty();

    if (data.length === 0) {
      tbody.append('<tr><td colspan="6" class="text-center text-muted">Tidak ada data</td></tr>');
      return;
    }

    data.forEach(item => {
      const statusBadge = item.status === 'aktif' ? 'badge-success' : 'badge-secondary';
      
      tbody.append(`
        <tr>
          <td>${item.id}</td>
          <td>${item.nama_kategori}</td>
          <td>${item.deskripsi || '-'}</td>
          <td><span class="badge ${statusBadge}">${item.status}</span></td>
          <td>${formatDate(item.created_at)}</td>
          <td>
            <div class="btn-group btn-group-sm">
              <button class="btn btn-info" onclick="editKategori(${item.id})" title="Edit">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn btn-danger" onclick="deleteKategori(${item.id})" title="Hapus">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      `);
    });

    // Reinitialize DataTable
    if ($.fn.DataTable.isDataTable('#kategori-table')) {
      $('#kategori-table').DataTable().destroy();
    }
    
    $('#kategori-table').DataTable({
      responsive: true,
      lengthChange: true,
      autoWidth: false,
      buttons: ['copy', 'csv', 'excel', 'pdf', 'print', 'colvis']
    }).buttons().container().appendTo('#kategori-table_wrapper .col-md-6:eq(0)');
  }

  function showAddModal() {
    $('#modal-title').text('Tambah Kategori');
    $('#kategori-form')[0].reset();
    $('#kategori-id').val('');
    $('#kategori-modal').modal('show');
  }

  function editKategori(id) {
    const item = kategoriData.find(k => k.id === id);
    if (!item) return;

    $('#modal-title').text('Edit Kategori');
    $('#kategori-id').val(item.id);
    $('#nama-kategori').val(item.nama_kategori);
    $('#deskripsi').val(item.deskripsi);
    $('#status').val(item.status);
    
    $('#kategori-modal').modal('show');
  }

  function saveKategori() {
    const data = {
      id: $('#kategori-id').val() || null,
      nama_kategori: $('#nama-kategori').val(),
      deskripsi: $('#deskripsi').val(),
      status: $('#status').val()
    };

    showLoading(true);
    google.script.run
      .withSuccessHandler(function(result) {
        showLoading(false);
        if (result.success) {
          showSuccess(result.message);
          $('#kategori-modal').modal('hide');
          loadKategoriData();
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
      })
      .saveKategori(data);
  }

  function deleteKategori(id) {
    const item = kategoriData.find(k => k.id === id);
    if (!item) return;

    Swal.fire({
      title: 'Hapus Kategori?',
      text: `Apakah Anda yakin ingin menghapus kategori "${item.nama_kategori}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Ya, Hapus',
      cancelButtonText: 'Batal'
    }).then((result) => {
      if (result.isConfirmed) {
        google.script.run
          .withSuccessHandler(function(result) {
            if (result.success) {
              showSuccess(result.message);
              loadKategoriData();
            } else {
              showError(result.message);
            }
          })
          .withFailureHandler(function(error) {
            showError('Error: ' + error.toString());
          })
          .deleteKategori(id);
      }
    });
  }
  </script>

</div>

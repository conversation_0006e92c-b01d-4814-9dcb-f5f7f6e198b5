<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>POS System - Laporan</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
    }
    .navbar {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .card {
      border: none;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;
      padding: 2rem;
    }
    .stat-number {
      font-size: 2rem;
      font-weight: bold;
    }
    .table th {
      background-color: #f8f9fa;
      border-top: none;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
      <a class="navbar-brand" href="?page=dashboard">
        <i class="fas fa-arrow-left me-2"></i>
        Laporan
      </a>
      
      <div class="navbar-nav ms-auto">
        <span class="navbar-text me-3">
          <i class="fas fa-user me-1"></i>
          <?= user ? user.nama : 'User' ?>
        </span>
        <a class="nav-link" href="#" onclick="logout()">
          <i class="fas fa-sign-out-alt"></i>
        </a>
      </div>
    </div>
  </nav>

  <div class="container-fluid mt-4">
    <!-- Filter Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-filter me-2"></i>
              Filter Laporan
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <label class="form-label">Tanggal Mulai</label>
                <input type="date" class="form-control" id="startDate">
              </div>
              <div class="col-md-3">
                <label class="form-label">Tanggal Akhir</label>
                <input type="date" class="form-control" id="endDate">
              </div>
              <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-primary d-block" onclick="filterTransactions()">
                  <i class="fas fa-search me-2"></i>
                  Filter
                </button>
              </div>
              <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-success d-block" onclick="exportData()">
                  <i class="fas fa-download me-2"></i>
                  Export
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card stat-card">
          <div class="card-body">
            <i class="fas fa-shopping-cart fa-2x mb-3"></i>
            <div class="stat-number" id="totalTransaksi">0</div>
            <div>Total Transaksi</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card stat-card">
          <div class="card-body">
            <i class="fas fa-money-bill-wave fa-2x mb-3"></i>
            <div class="stat-number" id="totalPendapatan">Rp 0</div>
            <div>Total Pendapatan</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card stat-card">
          <div class="card-body">
            <i class="fas fa-chart-line fa-2x mb-3"></i>
            <div class="stat-number" id="rataRata">Rp 0</div>
            <div>Rata-rata per Transaksi</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card stat-card">
          <div class="card-body">
            <i class="fas fa-calendar fa-2x mb-3"></i>
            <div class="stat-number" id="periodeLaporan">Hari Ini</div>
            <div>Periode Laporan</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Transactions Table -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-list me-2"></i>
              Daftar Transaksi
            </h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>ID Transaksi</th>
                    <th>Tanggal</th>
                    <th>Total</th>
                    <th>Bayar</th>
                    <th>Kembalian</th>
                    <th>Kasir</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody id="transactionTableBody">
                  <tr>
                    <td colspan="7" class="text-center">
                      <i class="fas fa-spinner fa-spin"></i> Memuat data...
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Transaction Detail Modal -->
  <div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Detail Transaksi</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body" id="detailContent">
          <!-- Detail content will be loaded here -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
          <button type="button" class="btn btn-primary" onclick="printReceipt()">
            <i class="fas fa-print me-2"></i>
            Print
          </button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    let allTransactions = [];
    let filteredTransactions = [];
    let detailModal;

    document.addEventListener('DOMContentLoaded', function() {
      detailModal = new bootstrap.Modal(document.getElementById('detailModal'));
      setDefaultDates();
      loadTransactions();
    });

    function setDefaultDates() {
      const today = new Date();
      const oneWeekAgo = new Date(today);
      oneWeekAgo.setDate(today.getDate() - 7);
      
      document.getElementById('endDate').value = today.toISOString().split('T')[0];
      document.getElementById('startDate').value = oneWeekAgo.toISOString().split('T')[0];
    }

    function loadTransactions() {
      google.script.run
        .withSuccessHandler(function(response) {
          if (response.success) {
            allTransactions = response.data;
            filterTransactions();
          } else {
            alert('Error: ' + response.message);
          }
        })
        .withFailureHandler(function(error) {
          alert('Error loading data: ' + error.toString());
        })
        .getTransaksiList();
    }

    function filterTransactions() {
      const startDate = new Date(document.getElementById('startDate').value);
      const endDate = new Date(document.getElementById('endDate').value);
      endDate.setHours(23, 59, 59, 999);

      filteredTransactions = allTransactions.filter(transaction => {
        const transactionDate = new Date(transaction.tanggal);
        return transactionDate >= startDate && transactionDate <= endDate;
      });

      displayTransactions(filteredTransactions);
      updateStatistics(filteredTransactions);
    }

    function displayTransactions(transactions) {
      const tbody = document.getElementById('transactionTableBody');
      tbody.innerHTML = '';

      if (transactions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">Tidak ada data</td></tr>';
        return;
      }

      transactions.forEach(transaction => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${transaction.id}</td>
          <td>${formatDate(transaction.tanggal)}</td>
          <td>${formatCurrency(transaction.total)}</td>
          <td>${formatCurrency(transaction.bayar)}</td>
          <td>${formatCurrency(transaction.kembalian)}</td>
          <td>${transaction.kasir}</td>
          <td>
            <button class="btn btn-sm btn-info" onclick="showDetail('${transaction.id}')" title="Detail">
              <i class="fas fa-eye"></i>
            </button>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    function updateStatistics(transactions) {
      const totalTransaksi = transactions.length;
      const totalPendapatan = transactions.reduce((sum, t) => sum + t.total, 0);
      const rataRata = totalTransaksi > 0 ? totalPendapatan / totalTransaksi : 0;

      document.getElementById('totalTransaksi').textContent = totalTransaksi;
      document.getElementById('totalPendapatan').textContent = formatCurrency(totalPendapatan);
      document.getElementById('rataRata').textContent = formatCurrency(rataRata);

      // Update period
      const startDate = document.getElementById('startDate').value;
      const endDate = document.getElementById('endDate').value;
      if (startDate && endDate) {
        document.getElementById('periodeLaporan').innerHTML = `${formatDateOnly(startDate)}<br>s/d<br>${formatDateOnly(endDate)}`;
      }
    }

    function showDetail(transactionId) {
      const transaction = allTransactions.find(t => t.id === transactionId);
      if (!transaction) return;

      const content = document.getElementById('detailContent');
      content.innerHTML = `
        <div class="row">
          <div class="col-md-6">
            <h6>Informasi Transaksi</h6>
            <table class="table table-sm">
              <tr><td>ID Transaksi:</td><td>${transaction.id}</td></tr>
              <tr><td>Tanggal:</td><td>${formatDate(transaction.tanggal)}</td></tr>
              <tr><td>Kasir:</td><td>${transaction.kasir}</td></tr>
            </table>
          </div>
          <div class="col-md-6">
            <h6>Informasi Pembayaran</h6>
            <table class="table table-sm">
              <tr><td>Total:</td><td>${formatCurrency(transaction.total)}</td></tr>
              <tr><td>Bayar:</td><td>${formatCurrency(transaction.bayar)}</td></tr>
              <tr><td>Kembalian:</td><td>${formatCurrency(transaction.kembalian)}</td></tr>
            </table>
          </div>
        </div>
      `;

      detailModal.show();
    }

    function exportData() {
      if (filteredTransactions.length === 0) {
        alert('Tidak ada data untuk diekspor');
        return;
      }

      let csvContent = "ID Transaksi,Tanggal,Total,Bayar,Kembalian,Kasir\n";
      
      filteredTransactions.forEach(transaction => {
        csvContent += `${transaction.id},${formatDate(transaction.tanggal)},${transaction.total},${transaction.bayar},${transaction.kembalian},${transaction.kasir}\n`;
      });

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `laporan_transaksi_${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    }

    function printReceipt() {
      window.print();
    }

    function formatCurrency(amount) {
      return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
    }

    function formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('id-ID') + ' ' + date.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit'
      });
    }

    function formatDateOnly(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('id-ID');
    }

    function logout() {
      if (confirm('Apakah Anda yakin ingin logout?')) {
        google.script.run
          .withSuccessHandler(function(response) {
            if (response.success) {
              window.location.href = '?page=login';
            }
          })
          .logout();
      }
    }
  </script>
</body>
</html>

<?!= include('header') ?>

<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <?!= include('navbar') ?>
  <?!= include('sidebar') ?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Laporan</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="?page=index">Home</a></li>
              <li class="breadcrumb-item active">Laporan</li>
            </ol>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <!-- Filter Section -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-filter mr-1"></i>
                  Filter Laporan
                </h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label>Tanggal Mulai</label>
                      <input type="date" class="form-control" id="start-date">
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label>Tanggal Akhir</label>
                      <input type="date" class="form-control" id="end-date">
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label>Jenis Laporan</label>
                      <select class="form-control" id="report-type">
                        <option value="penjualan">Laporan Penjualan</option>
                        <option value="produk">Laporan Produk</option>
                        <option value="stok">Laporan Stok</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label>&nbsp;</label>
                      <div>
                        <button class="btn btn-primary btn-block" onclick="generateReport()">
                          <i class="fas fa-chart-bar"></i> Generate Laporan
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Summary Cards -->
        <div class="row" id="summary-cards" style="display: none;">
          <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
              <div class="inner">
                <h3 id="total-transaksi">0</h3>
                <p>Total Transaksi</p>
              </div>
              <div class="icon">
                <i class="ion ion-bag"></i>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
              <div class="inner">
                <h3 id="total-pendapatan">Rp 0</h3>
                <p>Total Pendapatan</p>
              </div>
              <div class="icon">
                <i class="ion ion-stats-bars"></i>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
              <div class="inner">
                <h3 id="total-item">0</h3>
                <p>Total Item Terjual</p>
              </div>
              <div class="icon">
                <i class="ion ion-cube"></i>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
              <div class="inner">
                <h3 id="rata-rata">Rp 0</h3>
                <p>Rata-rata per Transaksi</p>
              </div>
              <div class="icon">
                <i class="ion ion-calculator"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Chart Section -->
        <div class="row" id="chart-section" style="display: none;">
          <div class="col-lg-8">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-chart-line mr-1"></i>
                  Grafik Penjualan
                </h3>
              </div>
              <div class="card-body">
                <canvas id="sales-chart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
              </div>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-chart-pie mr-1"></i>
                  Top 5 Produk
                </h3>
              </div>
              <div class="card-body">
                <canvas id="product-chart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Data Table -->
        <div class="row" id="table-section" style="display: none;">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-table mr-1"></i>
                  Detail Data
                </h3>
                <div class="card-tools">
                  <button class="btn btn-success btn-sm" onclick="exportReport()">
                    <i class="fas fa-file-excel"></i> Export Excel
                  </button>
                  <button class="btn btn-info btn-sm" onclick="printReport()">
                    <i class="fas fa-print"></i> Print
                  </button>
                </div>
              </div>
              <div class="card-body">
                <table class="table table-bordered table-striped" id="report-table">
                  <thead id="report-thead">
                  </thead>
                  <tbody id="report-tbody">
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <?!= include('footer') ?>

  <script>
  let currentReportData = [];
  let salesChart = null;
  let productChart = null;

  $(document).ready(function() {
    // Set default dates (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);
    
    $('#end-date').val(today.toISOString().split('T')[0]);
    $('#start-date').val(thirtyDaysAgo.toISOString().split('T')[0]);
  });

  function generateReport() {
    const startDate = $('#start-date').val();
    const endDate = $('#end-date').val();
    const reportType = $('#report-type').val();

    if (!startDate || !endDate) {
      showError('Pilih tanggal mulai dan akhir');
      return;
    }

    if (new Date(startDate) > new Date(endDate)) {
      showError('Tanggal mulai tidak boleh lebih besar dari tanggal akhir');
      return;
    }

    showLoading(true);

    const params = {
      startDate: startDate,
      endDate: endDate,
      reportType: reportType
    };

    google.script.run
      .withSuccessHandler(function(result) {
        showLoading(false);
        if (result.success) {
          currentReportData = result.data;
          displayReport(result.data, reportType);
          showSuccess('Laporan berhasil dibuat');
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
      })
      .generateReport(params);
  }

  function displayReport(data, reportType) {
    // Show sections
    $('#summary-cards').show();
    $('#chart-section').show();
    $('#table-section').show();

    // Update summary cards
    updateSummaryCards(data.summary);

    // Update charts
    updateCharts(data.chartData);

    // Update table
    updateTable(data.tableData, reportType);
  }

  function updateSummaryCards(summary) {
    $('#total-transaksi').text(summary.totalTransaksi || 0);
    $('#total-pendapatan').text(formatCurrency(summary.totalPendapatan || 0));
    $('#total-item').text(summary.totalItem || 0);
    $('#rata-rata').text(formatCurrency(summary.rataRata || 0));
  }

  function updateCharts(chartData) {
    // Sales Chart
    if (salesChart) {
      salesChart.destroy();
    }

    const salesCtx = document.getElementById('sales-chart').getContext('2d');
    salesChart = new Chart(salesCtx, {
      type: 'line',
      data: {
        labels: chartData.salesLabels || [],
        datasets: [{
          label: 'Penjualan',
          data: chartData.salesData || [],
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });

    // Product Chart
    if (productChart) {
      productChart.destroy();
    }

    const productCtx = document.getElementById('product-chart').getContext('2d');
    productChart = new Chart(productCtx, {
      type: 'doughnut',
      data: {
        labels: chartData.productLabels || [],
        datasets: [{
          data: chartData.productData || [],
          backgroundColor: [
            '#FF6384',
            '#36A2EB',
            '#FFCE56',
            '#4BC0C0',
            '#9966FF'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    });
  }

  function updateTable(tableData, reportType) {
    const thead = $('#report-thead');
    const tbody = $('#report-tbody');
    
    thead.empty();
    tbody.empty();

    if (!tableData || tableData.length === 0) {
      tbody.append('<tr><td colspan="100%" class="text-center text-muted">Tidak ada data</td></tr>');
      return;
    }

    // Create headers based on report type
    let headers = [];
    if (reportType === 'penjualan') {
      headers = ['No. Transaksi', 'Tanggal', 'Kasir', 'Pelanggan', 'Total', 'Status'];
    } else if (reportType === 'produk') {
      headers = ['Kode Barang', 'Nama Barang', 'Kategori', 'Qty Terjual', 'Total Pendapatan'];
    } else if (reportType === 'stok') {
      headers = ['Kode Barang', 'Nama Barang', 'Stok Saat Ini', 'Stok Minimum', 'Status'];
    }

    // Add headers
    const headerRow = '<tr>' + headers.map(h => `<th>${h}</th>`).join('') + '</tr>';
    thead.append(headerRow);

    // Add data rows
    tableData.forEach(row => {
      const dataRow = '<tr>' + row.map(cell => `<td>${cell}</td>`).join('') + '</tr>';
      tbody.append(dataRow);
    });

    // Initialize DataTable
    if ($.fn.DataTable.isDataTable('#report-table')) {
      $('#report-table').DataTable().destroy();
    }
    
    $('#report-table').DataTable({
      responsive: true,
      lengthChange: true,
      autoWidth: false,
      buttons: ['copy', 'csv', 'excel', 'pdf', 'print']
    }).buttons().container().appendTo('#report-table_wrapper .col-md-6:eq(0)');
  }

  function exportReport() {
    if (currentReportData.length === 0) {
      showError('Tidak ada data untuk diekspor');
      return;
    }

    showLoading(true);
    
    const params = {
      data: currentReportData,
      reportType: $('#report-type').val(),
      startDate: $('#start-date').val(),
      endDate: $('#end-date').val()
    };

    google.script.run
      .withSuccessHandler(function(result) {
        showLoading(false);
        if (result.success) {
          window.open(result.url, '_blank');
          showSuccess('Export berhasil');
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
      })
      .exportReport(params);
  }

  function printReport() {
    window.print();
  }
  </script>

</div>

<!-- Sidebar -->
<div class="sidebar" id="sidebar">
  <!-- Brand Logo -->
  <a href="?page=index" class="brand-link">
    <i class="fas fa-cash-register mr-2"></i>
    <span class="brand-text">POS System</span>
  </a>

  <!-- User Panel -->
  <div class="p-3 border-bottom">
    <div class="d-flex align-items-center text-white">
      <div class="mr-3">
        <i class="fas fa-user-circle fa-2x"></i>
      </div>
      <div>
        <div class="font-weight-bold"><?= user ? user.nama_lengkap : 'Guest' ?></div>
        <small class="text-muted"><?= user ? user.role : '' ?></small>
      </div>
    </div>
  </div>

  <!-- Navigation Menu -->
  <nav class="nav flex-column">
    <a class="nav-link <?= !e.parameter.page || e.parameter.page === 'index' ? 'active' : '' ?>" href="?page=index">
      <i class="fas fa-tachometer-alt"></i>
      <span>Dashboard</span>
    </a>
    
    <a class="nav-link <?= e.parameter.page === 'kasir' ? 'active' : '' ?>" href="?page=kasir">
      <i class="fas fa-cash-register"></i>
      <span>Kasir</span>
    </a>

    <!-- Master Data -->
    <div class="nav-item">
      <div class="nav-link text-muted small">
        <i class="fas fa-database"></i>
        <span>MASTER DATA</span>
      </div>
      <a class="nav-link ml-3 <?= e.parameter.page === 'barang' ? 'active' : '' ?>" href="?page=barang">
        <i class="fas fa-box"></i>
        <span>Data Barang</span>
      </a>
      <a class="nav-link ml-3 <?= e.parameter.page === 'kategori' ? 'active' : '' ?>" href="?page=kategori">
        <i class="fas fa-tags"></i>
        <span>Kategori</span>
      </a>
      <a class="nav-link ml-3 <?= e.parameter.page === 'satuan' ? 'active' : '' ?>" href="?page=satuan">
        <i class="fas fa-ruler"></i>
        <span>Satuan</span>
      </a>
      <a class="nav-link ml-3 <?= e.parameter.page === 'pelanggan' ? 'active' : '' ?>" href="?page=pelanggan">
        <i class="fas fa-users"></i>
        <span>Pelanggan</span>
      </a>
    </div>

    <a class="nav-link <?= e.parameter.page === 'laporan' ? 'active' : '' ?>" href="?page=laporan">
      <i class="fas fa-chart-bar"></i>
      <span>Laporan</span>
    </a>

    <?php if (user && user.role === 'administrator') { ?>
    <!-- Admin Only -->
    <div class="nav-item">
      <div class="nav-link text-muted small">
        <i class="fas fa-cogs"></i>
        <span>PENGATURAN</span>
      </div>
      <a class="nav-link ml-3 <?= e.parameter.page === 'users' ? 'active' : '' ?>" href="?page=users">
        <i class="fas fa-user-cog"></i>
        <span>Manajemen User</span>
      </a>
    </div>
    <?php } ?>

    <!-- Logout -->
    <a class="nav-link" href="#" onclick="logout()">
      <i class="fas fa-sign-out-alt"></i>
      <span>Logout</span>
    </a>
  </nav>
</div>

<!-- Main Content -->
<div class="main-content" id="main-content">
  <!-- Top Navbar -->
  <nav class="navbar navbar-expand-lg navbar-light top-navbar">
    <button class="btn btn-link d-md-none" id="sidebar-toggle">
      <i class="fas fa-bars"></i>
    </button>
    
    <div class="navbar-nav ml-auto">
      <!-- Search -->
      <div class="nav-item dropdown">
        <a class="nav-link" href="#" id="search-toggle">
          <i class="fas fa-search"></i>
        </a>
        <div class="dropdown-menu dropdown-menu-right" id="search-dropdown" style="display: none;">
          <form class="px-3 py-2" style="min-width: 300px;">
            <div class="input-group">
              <input type="text" class="form-control" placeholder="Cari barang..." id="global-search">
              <div class="input-group-append">
                <button class="btn btn-primary" type="submit">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>

      <!-- Notifications -->
      <div class="nav-item dropdown">
        <a class="nav-link" href="#" data-toggle="dropdown">
          <i class="fas fa-bell"></i>
          <span class="badge badge-warning badge-pill" id="notification-count">0</span>
        </a>
        <div class="dropdown-menu dropdown-menu-right">
          <h6 class="dropdown-header">Notifikasi</h6>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="#">
            <i class="fas fa-exclamation-triangle text-warning mr-2"></i>
            Stok barang menipis
          </a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item text-center" href="#">Lihat Semua</a>
        </div>
      </div>

      <!-- User Menu -->
      <div class="nav-item dropdown">
        <a class="nav-link" href="#" data-toggle="dropdown">
          <i class="fas fa-user"></i>
          <span class="d-none d-md-inline ml-1"><?= user ? user.nama_lengkap : 'Guest' ?></span>
        </a>
        <div class="dropdown-menu dropdown-menu-right">
          <h6 class="dropdown-header">
            <?= user ? user.nama_lengkap : 'Guest' ?>
            <br><small class="text-muted"><?= user ? user.role : '' ?></small>
          </h6>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="#">
            <i class="fas fa-user mr-2"></i> Profile
          </a>
          <a class="dropdown-item" href="#">
            <i class="fas fa-cog mr-2"></i> Settings
          </a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="#" onclick="logout()">
            <i class="fas fa-sign-out-alt mr-2"></i> Logout
          </a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Page Content -->
  <div class="container-fluid p-4">
    <!-- Content will be inserted here -->

<script>
// Sidebar toggle for mobile
document.getElementById('sidebar-toggle').addEventListener('click', function() {
  document.getElementById('sidebar').classList.toggle('show');
});

// Search toggle
document.getElementById('search-toggle').addEventListener('click', function(e) {
  e.preventDefault();
  const dropdown = document.getElementById('search-dropdown');
  dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
});

// Close search dropdown when clicking outside
document.addEventListener('click', function(e) {
  const searchToggle = document.getElementById('search-toggle');
  const searchDropdown = document.getElementById('search-dropdown');
  
  if (!searchToggle.contains(e.target) && !searchDropdown.contains(e.target)) {
    searchDropdown.style.display = 'none';
  }
});

// Global search functionality
document.getElementById('global-search').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    e.preventDefault();
    const keyword = this.value.trim();
    if (keyword) {
      window.location.href = '?page=barang&search=' + encodeURIComponent(keyword);
    }
  }
});

// Logout function
function logout() {
  Swal.fire({
    title: 'Logout',
    text: 'Apakah Anda yakin ingin keluar?',
    icon: 'question',
    showCancelButton: true,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Ya, Logout',
    cancelButtonText: 'Batal'
  }).then((result) => {
    if (result.isConfirmed) {
      google.script.run
        .withSuccessHandler(function(response) {
          if (response.success) {
            window.location.href = '?page=login';
          }
        })
        .withFailureHandler(function(error) {
          Swal.fire('Error', 'Gagal logout: ' + error.toString(), 'error');
        })
        .logout();
    }
  });
}
</script>

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>POS System - Login</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .login-card {
      background: white;
      border-radius: 15px;
      box-shadow: 0 15px 35px rgba(0,0,0,0.1);
      overflow: hidden;
      width: 100%;
      max-width: 400px;
    }
    .login-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem;
      text-align: center;
    }
    .login-body {
      padding: 2rem;
    }
    .form-control {
      border-radius: 10px;
      border: 2px solid #e9ecef;
      padding: 12px 15px;
    }
    .form-control:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .btn-login {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 10px;
      padding: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
    .btn-login:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    .demo-accounts {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 1rem;
      margin-top: 1rem;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <div class="login-card">
    <div class="login-header">
      <i class="fas fa-cash-register fa-3x mb-3"></i>
      <h2>POS System</h2>
      <p class="mb-0">Point of Sale</p>
    </div>
    
    <div class="login-body">
      <form id="loginForm">
        <div class="mb-3">
          <label class="form-label">Username</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-user"></i></span>
            <input type="text" class="form-control" id="username" placeholder="Masukkan username" required>
          </div>
        </div>
        
        <div class="mb-3">
          <label class="form-label">Password</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-lock"></i></span>
            <input type="password" class="form-control" id="password" placeholder="Masukkan password" required>
          </div>
        </div>
        
        <button type="submit" class="btn btn-primary btn-login w-100" id="loginBtn">
          <i class="fas fa-sign-in-alt me-2"></i>
          Masuk
        </button>
      </form>
      
      <div class="demo-accounts">
        <strong>Demo Accounts:</strong><br>
        <small>
          <i class="fas fa-user-shield"></i> Admin: admin / admin123<br>
          <i class="fas fa-user"></i> Kasir: kasir / kasir123
        </small>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.getElementById('loginForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();
      
      if (!username || !password) {
        alert('Username dan password harus diisi');
        return;
      }
      
      // Show loading
      const loginBtn = document.getElementById('loginBtn');
      const originalText = loginBtn.innerHTML;
      loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';
      loginBtn.disabled = true;
      
      // Call login function
      google.script.run
        .withSuccessHandler(function(response) {
          loginBtn.innerHTML = originalText;
          loginBtn.disabled = false;
          
          if (response.success) {
            alert('Login berhasil! Selamat datang, ' + response.user.nama);
            window.location.href = '?page=dashboard';
          } else {
            alert('Login gagal: ' + response.message);
            document.getElementById('password').value = '';
            document.getElementById('password').focus();
          }
        })
        .withFailureHandler(function(error) {
          loginBtn.innerHTML = originalText;
          loginBtn.disabled = false;
          alert('Error: ' + error.toString());
        })
        .login(username, password);
    });
    
    // Focus on username field
    document.getElementById('username').focus();
    
    // Enter key handling
    document.getElementById('password').addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        document.getElementById('loginForm').dispatchEvent(new Event('submit'));
      }
    });
  </script>
</body>
</html>

<?!= include('header') ?>

<body class="hold-transition login-page">
<div class="login-box">
  <!-- /.login-logo -->
  <div class="card card-outline card-primary">
    <div class="card-header text-center">
      <a href="#" class="h1"><b>POS</b>System</a>
    </div>
    <div class="card-body">
      <p class="login-box-msg"><PERSON><PERSON><PERSON> login untuk memulai sesi Anda</p>

      <div id="loginForm">
        <div class="input-group mb-3">
          <input type="text" class="form-control" placeholder="Username" id="username" name="username" required>
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-user"></span>
            </div>
          </div>
        </div>
        <div class="input-group mb-3">
          <input type="password" class="form-control" placeholder="Password" id="password" name="password" required>
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-lock"></span>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-8">
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="remember">
              <label class="form-check-label" for="remember">
                Ingat saya
              </label>
            </div>
          </div>
          <!-- /.col -->
          <div class="col-4">
            <button type="button" class="btn btn-primary btn-block" onclick="doLogin()">Masuk</button>
          </div>
          <!-- /.col -->
        </div>
      </div>

      <div class="social-auth-links text-center mt-2 mb-3">
        <p>- ATAU -</p>
        <button type="button" class="btn btn-block btn-success" onclick="loginDemo('admin')">
          <i class="fas fa-user-shield mr-2"></i> Demo Admin
        </button>
        <button type="button" class="btn btn-block btn-info" onclick="loginDemo('kasir')">
          <i class="fas fa-cash-register mr-2"></i> Demo Kasir
        </button>
      </div>

      <p class="mb-1">
        <a href="#" onclick="forgotPassword()">Lupa password?</a>
      </p>
      <p class="mb-0">
        <a href="#" onclick="showHelp()" class="text-center">Bantuan</a>
      </p>
    </div>
    <!-- /.card-body -->
  </div>
  <!-- /.card -->
</div>
<!-- /.login-box -->

<script>
$(document).ready(function() {
  // Focus on username field
  $('#username').focus();

  // Enter key handling
  $('#password').on('keypress', function(e) {
    if (e.which === 13) {
      doLogin();
    }
  });
});

function doLogin() {
  const username = $('#username').val().trim();
  const password = $('#password').val().trim();

  if (!username || !password) {
    alert('Username dan password harus diisi');
    return;
  }

  showLoading(true);

  // Use Google Apps Script run
  google.script.run
    .withSuccessHandler(function(response) {
      showLoading(false);

      if (response && response.success) {
        alert('Login berhasil! Selamat datang, ' + response.user.nama_lengkap);
        window.location.href = '?page=index';
      } else {
        alert('Login gagal: ' + (response ? response.message : 'Unknown error'));
        $('#password').val('').focus();
      }
    })
    .withFailureHandler(function(error) {
      showLoading(false);
      alert('Error: ' + error.toString());
      console.error('Login error:', error);
    })
    .login(username, password);
}

function loginDemo(role) {
  if (role === 'admin') {
    $('#username').val('admin');
    $('#password').val('admin123');
  } else {
    $('#username').val('kasir');
    $('#password').val('kasir123');
  }
  
  Swal.fire({
    title: 'Demo Login',
    text: 'Username dan password telah diisi. Klik "Masuk" untuk melanjutkan.',
    icon: 'info',
    confirmButtonText: 'OK'
  }).then(() => {
    $('#username').focus();
  });
}

function forgotPassword() {
  Swal.fire({
    title: 'Lupa Password',
    html: `
      <p>Untuk reset password, silakan hubungi administrator sistem.</p>
      <p><strong>Email:</strong> <EMAIL></p>
      <p><strong>Telepon:</strong> +62 ************</p>
    `,
    icon: 'info',
    confirmButtonText: 'OK'
  });
}

function showHelp() {
  Swal.fire({
    title: 'Bantuan Login',
    html: `
      <div class="text-left">
        <h5>Cara Login:</h5>
        <ol>
          <li>Masukkan username dan password</li>
          <li>Klik tombol "Masuk" atau tekan Enter</li>
          <li>Jika berhasil, Anda akan diarahkan ke dashboard</li>
        </ol>
        
        <h5>Demo Account:</h5>
        <ul>
          <li><strong>Admin:</strong> admin / admin123</li>
          <li><strong>Kasir:</strong> kasir / kasir123</li>
        </ul>
        
        <h5>Troubleshooting:</h5>
        <ul>
          <li>Pastikan username dan password benar</li>
          <li>Pastikan akun dalam status aktif</li>
          <li>Hubungi administrator jika masih bermasalah</li>
        </ul>
      </div>
    `,
    icon: 'info',
    confirmButtonText: 'Tutup'
  });
}

// Global functions for this page
function showLoading(show = true) {
  const btn = $('#loginForm button[type="submit"]');
  if (show) {
    btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Loading...');
  } else {
    btn.prop('disabled', false).html('Masuk');
  }
}

function showSuccess(message) {
  Swal.fire('Berhasil', message, 'success');
}

function showError(message) {
  Swal.fire('Error', message, 'error');
}
</script>

<?!= include('footer') ?>

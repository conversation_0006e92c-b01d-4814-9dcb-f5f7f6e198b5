<?!= include('header') ?>

<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <?!= include('navbar') ?>
  <?!= include('sidebar') ?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Data Satuan</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="?page=index">Home</a></li>
              <li class="breadcrumb-item"><a href="#">Master Data</a></li>
              <li class="breadcrumb-item active">Satuan</li>
            </ol>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-ruler mr-1"></i>
                  Daftar Satuan
                </h3>
                <div class="card-tools">
                  <button class="btn btn-primary btn-sm" onclick="showAddModal()">
                    <i class="fas fa-plus"></i> Tambah Satuan
                  </button>
                </div>
              </div>
              <div class="card-body">
                <table class="table table-bordered table-striped" id="satuan-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Nama Satuan</th>
                      <th>Singkatan</th>
                      <th>Status</th>
                      <th>Tanggal Dibuat</th>
                      <th>Aksi</th>
                    </tr>
                  </thead>
                  <tbody id="satuan-tbody">
                    <tr>
                      <td colspan="6" class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Memuat data...
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- Add/Edit Modal -->
  <div class="modal fade" id="satuan-modal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title" id="modal-title">Tambah Satuan</h4>
          <button type="button" class="close" data-dismiss="modal">
            <span>&times;</span>
          </button>
        </div>
        <form id="satuan-form">
          <div class="modal-body">
            <input type="hidden" id="satuan-id">
            <div class="form-group">
              <label for="nama-satuan">Nama Satuan *</label>
              <input type="text" class="form-control" id="nama-satuan" required placeholder="Contoh: Kilogram">
            </div>
            <div class="form-group">
              <label for="singkatan">Singkatan *</label>
              <input type="text" class="form-control" id="singkatan" required placeholder="Contoh: kg" maxlength="10">
            </div>
            <div class="form-group">
              <label for="status">Status</label>
              <select class="form-control" id="status">
                <option value="aktif">Aktif</option>
                <option value="nonaktif">Non Aktif</option>
              </select>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
            <button type="submit" class="btn btn-primary">Simpan</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <?!= include('footer') ?>

  <script>
  let satuanData = [];

  $(document).ready(function() {
    loadSatuanData();
    setupEventHandlers();
  });

  function setupEventHandlers() {
    // Form submission
    $('#satuan-form').on('submit', function(e) {
      e.preventDefault();
      saveSatuan();
    });

    // Initialize DataTable
    $('#satuan-table').DataTable({
      responsive: true,
      lengthChange: true,
      autoWidth: false,
      buttons: ['copy', 'csv', 'excel', 'pdf', 'print', 'colvis']
    }).buttons().container().appendTo('#satuan-table_wrapper .col-md-6:eq(0)');
  }

  function loadSatuanData() {
    google.script.run
      .withSuccessHandler(function(result) {
        if (result.success) {
          satuanData = result.data;
          displaySatuanData(satuanData);
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showError('Error loading data: ' + error.toString());
      })
      .getSatuanList();
  }

  function displaySatuanData(data) {
    const tbody = $('#satuan-tbody');
    tbody.empty();

    if (data.length === 0) {
      tbody.append('<tr><td colspan="6" class="text-center text-muted">Tidak ada data</td></tr>');
      return;
    }

    data.forEach(item => {
      const statusBadge = item.status === 'aktif' ? 'badge-success' : 'badge-secondary';
      
      tbody.append(`
        <tr>
          <td>${item.id}</td>
          <td>${item.nama_satuan}</td>
          <td><span class="badge badge-info">${item.singkatan}</span></td>
          <td><span class="badge ${statusBadge}">${item.status}</span></td>
          <td>${formatDate(item.created_at)}</td>
          <td>
            <div class="btn-group btn-group-sm">
              <button class="btn btn-info" onclick="editSatuan(${item.id})" title="Edit">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn btn-danger" onclick="deleteSatuan(${item.id})" title="Hapus">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      `);
    });

    // Reinitialize DataTable
    if ($.fn.DataTable.isDataTable('#satuan-table')) {
      $('#satuan-table').DataTable().destroy();
    }
    
    $('#satuan-table').DataTable({
      responsive: true,
      lengthChange: true,
      autoWidth: false,
      buttons: ['copy', 'csv', 'excel', 'pdf', 'print', 'colvis']
    }).buttons().container().appendTo('#satuan-table_wrapper .col-md-6:eq(0)');
  }

  function showAddModal() {
    $('#modal-title').text('Tambah Satuan');
    $('#satuan-form')[0].reset();
    $('#satuan-id').val('');
    $('#satuan-modal').modal('show');
  }

  function editSatuan(id) {
    const item = satuanData.find(s => s.id === id);
    if (!item) return;

    $('#modal-title').text('Edit Satuan');
    $('#satuan-id').val(item.id);
    $('#nama-satuan').val(item.nama_satuan);
    $('#singkatan').val(item.singkatan);
    $('#status').val(item.status);
    
    $('#satuan-modal').modal('show');
  }

  function saveSatuan() {
    const data = {
      id: $('#satuan-id').val() || null,
      nama_satuan: $('#nama-satuan').val(),
      singkatan: $('#singkatan').val(),
      status: $('#status').val()
    };

    showLoading(true);
    google.script.run
      .withSuccessHandler(function(result) {
        showLoading(false);
        if (result.success) {
          showSuccess(result.message);
          $('#satuan-modal').modal('hide');
          loadSatuanData();
        } else {
          showError(result.message);
        }
      })
      .withFailureHandler(function(error) {
        showLoading(false);
        showError('Error: ' + error.toString());
      })
      .saveSatuan(data);
  }

  function deleteSatuan(id) {
    const item = satuanData.find(s => s.id === id);
    if (!item) return;

    Swal.fire({
      title: 'Hapus Satuan?',
      text: `Apakah Anda yakin ingin menghapus satuan "${item.nama_satuan}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Ya, Hapus',
      cancelButtonText: 'Batal'
    }).then((result) => {
      if (result.isConfirmed) {
        google.script.run
          .withSuccessHandler(function(result) {
            if (result.success) {
              showSuccess(result.message);
              loadSatuanData();
            } else {
              showError(result.message);
            }
          })
          .withFailureHandler(function(error) {
            showError('Error: ' + error.toString());
          })
          .deleteSatuan(id);
      }
    });
  }
  </script>

</div>

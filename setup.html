<?!= include('header') ?>

<body class="hold-transition login-page">
<div class="login-box">
  <div class="card card-outline card-primary">
    <div class="card-header text-center">
      <a href="#" class="h1"><b>POS</b>System</a>
      <p class="login-box-msg">Setup Awal Aplikasi</p>
    </div>
    <div class="card-body">
      <div class="alert alert-info">
        <h5><i class="icon fas fa-info"></i> Setup Diperlukan!</h5>
        Aplikasi POS belum diinisialisasi. Klik tombol di bawah untuk membuat data awal.
      </div>

      <div id="setup-status" style="display: none;">
        <div class="progress mb-3">
          <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
        </div>
        <div class="text-center">
          <i class="fas fa-spinner fa-spin"></i> Membuat data awal...
        </div>
      </div>

      <div id="setup-result" style="display: none;">
        <div class="alert alert-success">
          <h5><i class="icon fas fa-check"></i> Setup Berhasil!</h5>
          <p>Data awal telah dibuat:</p>
          <ul>
            <li>User Administrator (admin/admin123)</li>
            <li>User Kasir (kasir/kasir123)</li>
            <li>Kategori default</li>
            <li>Satuan default</li>
          </ul>
        </div>
      </div>

      <div id="setup-buttons">
        <button type="button" class="btn btn-primary btn-block btn-lg" onclick="runSetup()">
          <i class="fas fa-cogs"></i> Jalankan Setup
        </button>
        
        <div class="mt-3">
          <button type="button" class="btn btn-success btn-block" onclick="manualSetup()">
            <i class="fas fa-book"></i> Panduan Setup Manual
          </button>
        </div>
      </div>

      <div id="login-button" style="display: none;">
        <a href="?page=login" class="btn btn-success btn-block btn-lg">
          <i class="fas fa-sign-in-alt"></i> Lanjut ke Login
        </a>
      </div>
    </div>
  </div>
</div>

<script>
function runSetup() {
  // Hide buttons and show progress
  $('#setup-buttons').hide();
  $('#setup-status').show();
  
  let progress = 0;
  const progressBar = $('.progress-bar');
  
  // Animate progress
  const interval = setInterval(() => {
    progress += 20;
    progressBar.css('width', progress + '%');
    if (progress >= 80) {
      clearInterval(interval);
    }
  }, 500);

  // Run initialization
  google.script.run
    .withSuccessHandler(function(result) {
      clearInterval(interval);
      progressBar.css('width', '100%');
      
      setTimeout(() => {
        $('#setup-status').hide();
        
        if (result.success) {
          $('#setup-result').show();
          $('#login-button').show();
          
          Swal.fire({
            title: 'Setup Berhasil!',
            text: 'Aplikasi POS siap digunakan',
            icon: 'success',
            confirmButtonText: 'Lanjut ke Login'
          }).then(() => {
            window.location.href = '?page=login';
          });
        } else {
          Swal.fire('Setup Gagal', result.message, 'error');
          $('#setup-buttons').show();
        }
      }, 1000);
    })
    .withFailureHandler(function(error) {
      clearInterval(interval);
      $('#setup-status').hide();
      $('#setup-buttons').show();
      
      Swal.fire('Error', 'Gagal menjalankan setup: ' + error.toString(), 'error');
    })
    .initializeDefaultData();
}

function manualSetup() {
  Swal.fire({
    title: 'Panduan Setup Manual',
    html: `
      <div class="text-left">
        <h5>Jika setup otomatis gagal, ikuti langkah berikut:</h5>
        <ol>
          <li><strong>Buka Google Sheets "pos"</strong></li>
          <li><strong>Pastikan sheet sudah dibuat:</strong>
            <ul>
              <li>users</li>
              <li>barang</li>
              <li>kategori</li>
              <li>satuan</li>
              <li>penjualan</li>
              <li>penjualan_item</li>
              <li>pelanggan</li>
            </ul>
          </li>
          <li><strong>Isi sheet "users" dengan data:</strong>
            <br>Header: id, username, password, nama_lengkap, role, status, created_at, last_login
            <br>Data 1: 1, admin, admin123, Administrator, administrator, aktif, [tanggal], 
            <br>Data 2: 2, kasir, kasir123, Kasir, kasir, aktif, [tanggal], 
          </li>
          <li><strong>Buka Apps Script Editor</strong></li>
          <li><strong>Jalankan fungsi initializeDefaultData()</strong></li>
          <li><strong>Refresh halaman ini</strong></li>
        </ol>
        
        <h5>Atau:</h5>
        <p>Hubungi administrator untuk bantuan setup.</p>
      </div>
    `,
    icon: 'info',
    confirmButtonText: 'Mengerti',
    width: '600px'
  });
}

// Check if already initialized
$(document).ready(function() {
  // You can add auto-check here if needed
});
</script>

<?!= include('footer') ?>

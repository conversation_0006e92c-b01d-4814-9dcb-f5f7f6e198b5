<!-- Main Sidebar Container -->
<aside class="main-sidebar sidebar-dark-primary elevation-4" style="position: fixed !important; top: 0; left: 0; height: 100vh; width: 250px; z-index: 1040; display: block !important; background: #343a40;">
  <!-- Brand Logo -->
  <a href="?page=index" class="brand-link" style="display: block; padding: 1rem; color: white; text-decoration: none; border-bottom: 1px solid #495057;">
    <i class="fas fa-cash-register mr-2"></i>
    <span class="brand-text font-weight-light">POS System</span>
  </a>

  <!-- Sidebar -->
  <div class="sidebar">
    <!-- Sidebar user panel (optional) -->
    <div class="user-panel mt-3 pb-3 mb-3 d-flex">
      <div class="image">
        <img src="https://via.placeholder.com/160x160?text=U" class="img-circle elevation-2" alt="User Image">
      </div>
      <div class="info">
        <a href="#" class="d-block">User</a>
        <small class="text-muted">Role</small>
      </div>
    </div>

    <!-- SidebarSearch Form -->
    <div class="form-inline">
      <div class="input-group" data-widget="sidebar-search">
        <input class="form-control form-control-sidebar" type="search" placeholder="Search" aria-label="Search">
        <div class="input-group-append">
          <button class="btn btn-sidebar">
            <i class="fas fa-search fa-fw"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Sidebar Menu -->
    <nav class="mt-2">
      <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
        
        <!-- Dashboard -->
        <li class="nav-item">
          <a href="?page=index" class="nav-link">
            <i class="nav-icon fas fa-tachometer-alt"></i>
            <p>Dashboard</p>
          </a>
        </li>

        <!-- Kasir -->
        <li class="nav-item">
          <a href="?page=kasir" class="nav-link">
            <i class="nav-icon fas fa-cash-register"></i>
            <p>Kasir</p>
          </a>
        </li>

        <!-- Master Data -->
        <li class="nav-item">
          <a href="#" class="nav-link">
            <i class="nav-icon fas fa-database"></i>
            <p>
              Master Data
              <i class="right fas fa-angle-left"></i>
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="?page=barang" class="nav-link">
                <i class="far fa-circle nav-icon"></i>
                <p>Data Barang</p>
              </a>
            </li>
            <li class="nav-item">
              <a href="?page=kategori" class="nav-link">
                <i class="far fa-circle nav-icon"></i>
                <p>Kategori</p>
              </a>
            </li>
            <li class="nav-item">
              <a href="?page=satuan" class="nav-link">
                <i class="far fa-circle nav-icon"></i>
                <p>Satuan</p>
              </a>
            </li>
            <li class="nav-item">
              <a href="?page=pelanggan" class="nav-link">
                <i class="far fa-circle nav-icon"></i>
                <p>Pelanggan</p>
              </a>
            </li>
          </ul>
        </li>

        <!-- Transaksi -->
        <li class="nav-item">
          <a href="#" class="nav-link">
            <i class="nav-icon fas fa-shopping-cart"></i>
            <p>
              Transaksi
              <i class="right fas fa-angle-left"></i>
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="?page=penjualan" class="nav-link">
                <i class="far fa-circle nav-icon"></i>
                <p>Riwayat Penjualan</p>
              </a>
            </li>
            <li class="nav-item">
              <a href="?page=retur" class="nav-link">
                <i class="far fa-circle nav-icon"></i>
                <p>Retur</p>
              </a>
            </li>
          </ul>
        </li>

        <!-- Laporan -->
        <li class="nav-item">
          <a href="?page=laporan" class="nav-link">
            <i class="nav-icon fas fa-chart-bar"></i>
            <p>Laporan</p>
          </a>
        </li>

        <!-- Pengaturan -->
        <li class="nav-header">PENGATURAN</li>

        <li class="nav-item">
          <a href="?page=users" class="nav-link">
            <i class="nav-icon fas fa-users"></i>
            <p>Manajemen User</p>
          </a>
        </li>

        <!-- Divider -->
        <li class="nav-header">BANTUAN</li>
        
        <li class="nav-item">
          <a href="#" class="nav-link" onclick="showHelp()">
            <i class="nav-icon fas fa-question-circle"></i>
            <p>Bantuan</p>
          </a>
        </li>

        <li class="nav-item">
          <a href="#" class="nav-link" onclick="showAbout()">
            <i class="nav-icon fas fa-info-circle"></i>
            <p>Tentang</p>
          </a>
        </li>

      </ul>
    </nav>
    <!-- /.sidebar-menu -->
  </div>
  <!-- /.sidebar -->
</aside>

<script>
function showHelp() {
  Swal.fire({
    title: 'Bantuan POS System',
    html: `
      <div class="text-left">
        <h5>Panduan Penggunaan:</h5>
        <ul>
          <li><strong>Dashboard:</strong> Melihat ringkasan penjualan dan statistik</li>
          <li><strong>Kasir:</strong> Melakukan transaksi penjualan</li>
          <li><strong>Master Data:</strong> Mengelola data barang, kategori, satuan, dan pelanggan</li>
          <li><strong>Laporan:</strong> Melihat laporan penjualan dan analisis</li>
        </ul>
        <h5>Shortcut Keyboard:</h5>
        <ul>
          <li><strong>F1:</strong> Bantuan</li>
          <li><strong>F2:</strong> Kasir</li>
          <li><strong>F3:</strong> Cari Barang</li>
          <li><strong>Ctrl+S:</strong> Simpan</li>
        </ul>
      </div>
    `,
    icon: 'info',
    confirmButtonText: 'Tutup'
  });
}

function showAbout() {
  Swal.fire({
    title: 'Tentang POS System',
    html: `
      <div class="text-center">
        <h4>POS System v1.0</h4>
        <p>Sistem Point of Sale berbasis Google Apps Script</p>
        <p>Dikembangkan dengan AdminLTE 3.2.0</p>
        <hr>
        <p><small>© 2024 POS System. All rights reserved.</small></p>
      </div>
    `,
    icon: 'info',
    confirmButtonText: 'Tutup'
  });
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
  if (e.key === 'F1') {
    e.preventDefault();
    showHelp();
  } else if (e.key === 'F2') {
    e.preventDefault();
    window.location.href = '?page=kasir';
  } else if (e.key === 'F3') {
    e.preventDefault();
    document.querySelector('[data-widget="navbar-search"]').click();
  }
});
</script>
